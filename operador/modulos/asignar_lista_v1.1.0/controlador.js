const URL_MODULO = 'modulos/asignar_lista_v1.1.0';
var tipo_ac = 0;
var fila_s = 0;
var dtable;
var dtable2;
var data;
var tipo_id;
var tipo;
var nombre_tipo;

$(document).ready(function () {
    traerRegionales("reg");

    $("#button_ver").click(function (event) {
        event.preventDefault();
        mostrar_tabla_consulta();
    });

    $("#enviar").click(function (event) {
        asignar_lista($(this).attr("data-distriasi"));
    });

    $('.modal').on('hidden.bs.modal', function (event) {
        cerrar_moda_asig_list();
    });

    $("#lista").change(function () {
        $('#itemlist tbody').html('');
        var id_lista = $(this).val();
        if (id_lista != "") {
            cargar_items(id_lista);
        }
    });
});

function mostrar_selects(id, value) {
    if (value == "") {
        $("#" + id).hide();
        switch (id) {
            case 'regd':
                $("#distrid").hide();
                $("#channel").hide();
                break;
            case 'channel':
                $("#distrid").hide();
                break;
        }
    } else {
        $("#" + id).show();
    }
}

//LLENAR SELECTS
function traerCanales(select) {

    $.post(
        'modulos/asignar_lista_v1.0.1/controlador.php',
        {
            accion: "traerCanales"
        },
        function (data) {
            data = JSON.parse(data);
            var sel = "<option value=''>SELECCIONAR</option>";
            $.each(data, function (index, fila) {
                sel += "<option value='" + fila.id + "'>" + fila.nombre + "</option>";
            });
            $("#" + select).html(sel).change();
        });
}

function traerDistribuidores(select, regional) {

    $.post(
        'modulos/asignar_lista_v1.0.1/controlador.php',
        {
            accion: "traerDistribuidores",
            regional: regional
        },
        function (data) {
            data = JSON.parse(data);
            var sel;
            var sel2 = "<option value='' selected>SELECCIONAR</option>";
            var distribs = "";
            $.each(data, function (index, fila) {
                distribs += fila.id + ',';
                sel += "<option value='" + fila.id + "'>" + fila.nombre + "</option>";
            });
            distribs = distribs.substring(0, (distribs.length) - 1);
            sel2 += "<option value ='" + distribs + "'>Todos los distribuidores</option>";
            $("#" + select).html(sel2 + sel).change();
        });
}

function traerRegionales(select) {

    $.post(
        'modulos/asignar_lista_v1.0.1/controlador.php',
        {
            accion: "traerRegionales"
        },
        function (data) {
            data = JSON.parse(data);
            var sel;
            var sel2 = "<option value='' selected>SELECCIONAR</option>";
            var regs = "";
            $.each(data, function (index, fila) {
                regs += fila.id + ',';
                sel += "<option value='" + fila.id + "'>" + fila.nombre + "</option>";
            });
            regs = regs.substring(0, (regs.length) - 1);
            sel2 += "<option value ='" + regs + "'>Todas las regionales</option>";
            $("#" + select).html(sel2 + sel);
        });
}
//LLENAR SELECTS FIN

function mostrar_tabla_consulta() {
    var reg = $("#reg").val();
    var tipo = 0;
    var id_tipo = 0;

    if (reg != "") {
        tipo = 1;
        id_tipo = reg;
    }
    else {
        tipo = 0;
        id_tipo = 0;
    }
    if (!$.fn.DataTable.isDataTable('#tblListas')) {
        dtable = $("#tblListas").DataTable({
            "ajax": {
                "url": "modulos/asignar_lista_v1.0.1/controlador.php",
                "type": "POST",
                "deferRender": false,
                "data": {
                    accion: 'consulta_tabla',
                    tipo2: tipo,
                    id_tipo2: id_tipo
                },
            },
            "bFilter": false, "responsive": true,
            "columns": [
                { "data": "tipo" },
                { "data": "nombre_tipo" },
                { "data": "estado_vigencia", className: "text-center" },
                { "data": "nombre_lista" },
                { "data": "nombre_lista", className: "text-center" },
                { "data": "nombre_lista", className: "text-center" }
            ],
            "columnDefs": [{
                "targets": 2,
                "data": "estado_vigencia",
                render: function (data, type, row, rowcol) {
                    if (data == 1)
                        return '<img class="" src="template/images/tick.png" title=" Lista Activa ">';
                    if (data == 0)
                        return '<img class="" src="template/images/publish.png" title=" Lista Inactiva ">';
                }
            }, {
                "targets": 3,
                "data": "",
                render: function (data, type, row, rowcol) {
                    if (row["estado"] != 0)
                        return row["nombre_lista"];
                    else {
                        return 'GENERAL';
                    }
                }
            }, {
                "targets": 4,
                "data": "nombre_lista",
                render: function (data, type, row, rowcol) {
                    var button = "";
                    var array_dis = new Array();
                    var dis = -1;

                    if (row["id_distribuidor"] != null && row["id_distribuidor"] != "") {
                        array_dis = row["id_distribuidor"].split(",")
                        for (var i = 1; i < 10; i++) {
                            dis = parseInt(row["id_distribuidor"].indexOf(i.toString()));
                            if (dis >= 0) {
                                i = 10;
                            }
                        }
                    }
                    if (data != 'GENERAL' && row["estado"] == 1 && $.inArray("0", array_dis) >= 0)
                        button += '<button class="btn btn-sm btn-danger back" data-row="' + rowcol.row + '"><i class="fas fa-sign-out-alt"></i></button>&nbsp;';
                    if (row["estado"] == 1 && dis >= 0) {
                        button += '<button class="btn btn-sm btn-success viwe_list" data-row="' + rowcol.row + '"><i class="fas fa-eye"></i></button>&nbsp;';
                    }

                    return button;
                }
            },
            {
                "targets": 5,
                "data": "nombre_lista",
                render: function (data, type, row, rowcol) {
                    var dis = -1;
                    var array_dis = new Array();
                    if (row["id_distribuidor"] != null && row["id_distribuidor"] != "") {
                        array_dis = row["id_distribuidor"].split(",");
                        for (var i = 1; i < 10; i++) {
                            dis = parseInt(row["id_distribuidor"].indexOf(i.toString()));
                            if (dis >= 0) {
                                i = 10;
                            }
                        }
                    }
                    if (data == 'GENERAL' || row["estado"] == 0 || (dis >= 0 && $.inArray("0", array_dis) < 0) || array_dis.length == 0) {
                        return '<button class="btn btn-sm btn-default edit" data-row="' + rowcol.row + '"><i class="glyphicon glyphicon-zoom-in"></i></button>';
                    } else {
                        return '';
                    }
                }
            }],
            fnDrawCallback: function () {
                $(".edit").unbind("click");
                $(".edit").click(function () {
                    fila_s = $(this).attr("data-row");
                    data = dtable.row($(this).parents('tr')).data();
                    tipo_id = data['tipo_id'];
                    tipo = data['tiponum'];
                    nombre_tipo = data['nombre_tipo'];
                    nombre_lista = data['nombre_lista'];

                    var dis = -1;

                    if (data["id_distribuidor"] != null && data["id_distribuidor"] != "") {
                        for (var i = 1; i < 10; i++) {
                            dis = parseInt(data["id_distribuidor"].indexOf(i.toString()));
                            if (dis >= 0) {
                                i = 10;
                            }
                        }
                    }
                    if (dis >= 0) {
                        $("#enviar").attr("data-distriasi", "1");
                    } else {
                        $("#enviar").attr("data-distriasi", "0");
                    }

                    mostrar_listas_modal(tipo, tipo_id, nombre_tipo, nombre_lista);

                });
                $(".viwe_list").unbind("click");
                $(".viwe_list").click(function () {
                    fila_s = $(this).attr("data-row");
                    data = dtable.row($(this).parents('tr')).data();
                    id_lista = data['id_lista'];
                    nombre_tipo = data['nombre_tipo'];
                    consultar_listas_items_distrib(id_lista, nombre_tipo);

                });
                $(".back").unbind("click");
                $(".back").click(function () {
                    fila_s = $(this).attr("data-row");
                    var data = dtable.row($(this).parents('tr')).data();
                    tipo_id1 = data['tipo_id'];
                    tipo1 = data['tiponum'];
                    id_lista1 = data['id_lista'];
                    if (data["id_distribuidor"] > 0) {
                        $("#enviar").attr("data-distriasi", "1");
                    } else {
                        $("#enviar").attr("data-distriasi", "0");
                    }

                    BootstrapDialog.confirm("¿Desea quitar esta lista?.", function (result) {
                        if (result) {
                            quitar_lista(id_lista1, tipo_id1, tipo1);
                        }

                    });
                })
            }
        });
        $("#div_listas").show();
    } else {
        dtable.destroy();
        mostrar_tabla_consulta();
    }
}

function consultar_listas_items_distrib(id_lista, nombre_tipo) {
    html = '';
    $("#itemlistc").html("");
    $('#nlevel').html('<div class="text-center">Nombre de la Regional: &nbsp;&nbsp;&nbsp;<strong>' + nombre_tipo + '</strong></div>');

    $("#listad").hide();
    $("#cl_bt").html("Aceptar");
    $("#cl_bt").addClass("btn-primary");
    $("#enviar").hide();

    var nombre_t_list = "<strong>Lista de Precios del distribuidor</strong>";
    var t_modal = "Lista asignada por distribuidores";

    $.post('modulos/asignar_lista_v1.0.1/controlador.php',
        {
            accion: 'consultar_listas_items_distrib', id_lista: id_lista
        },
        function (data, textStatus) {
            data = JSON.parse(data);
            var id_lista = 0;
            var id_lista_tem = 0;
            var num_titulos = 0;
            $.each(data, function (index, fila) {

                var precio_venta = fila.precio_pdv;
                var precioventa_directo = fila.precio_publico;
                if (precio_venta.indexOf('.') > 0) {
                    precio_venta = format(precio_venta, 2).replace(",", "");
                }
                if (precioventa_directo.indexOf('.') > 0) {
                    precioventa_directo = format(precioventa_directo, 2).replace(",", "");
                }

                id_lista = fila.id_lista;
                if (id_lista == id_lista_tem) {

                } else {
                    if (html != "") {
                        html += "</tbody></table></div></div>";
                    }

                    html += '<div class="panel panel-default"><div class="panel-heading"><h5>Nombre distribuidor: ' + fila.distrib + '<br>Nombre lista: ' + fila.lista_n + '</h5></div><div class="panel-body">'
                        + '<table id="itemlist" class="table dt-responsive nowrap" >'
                        + '<thead>'
                        + '<th align="center" class="all">Nombre SKU</th>'
                        + '<th align="center">SKU</th>'
                        + '<th align="center">Precio pdv</th>'
                        + '</thead><tbody>';
                    num_titulos++;
                }
                id_lista_tem = fila.id_lista;

                html += '<tr><td>' + fila.producto + '</td><td>' + fila.pn + '</td><td>' + precio_venta + '</td></tr>';

            });
            html += '<input type="hidden" id="tipo_tip">'
                + '<input type="hidden" id="nom_lista">';

            $("#itemlistc").html(html);
            if (num_titulos > 1) {
                nombre_t_list = "<strong>Listas de Precios de los distribuidores</strong>";
                t_modal = "Listas asignadas por los distribuidores";
            }
            $("#lp_nom").html("");
            $('#titulo_modal').html(t_modal);
            $("#itemlistc").show();
            $(".modal").modal();
        });
}

function mostrar_listas_modal(tipo, id_tipo, nombre_tipo, nombre_lista) {
    $("#tipo_tip").val(tipo);
    $("#nom_lista").val(nombre_lista);
    $("#itemlistc").html("");
    switch (tipo) {
        case '1':
            $('#titulo_modal').html('Crear y editar asignacion de lista para Regional');
            $('#nlevel').html('<div class="text-center">Nombre de la Regional: &nbsp;&nbsp;&nbsp;<strong>' + nombre_tipo + '</strong></div>');
            break;
    }
    llenar_select_listas();
    $(".modal").modal();
    $("#listad").show();
    $("#cl_bt").html("Cancelar");
    $("#enviar").show();
    $("#cl_bt").removeClass("btn-primary");
    $("#lp_nom").html("<strong>Lista de Precios:</strong>");
}

function llenar_select_listas() {
    var html_select = "<option value=''>SELECCIONAR</option>";
    $.post('modulos/asignar_lista_v1.0.1/controlador.php',
        {
            accion: 'consultar_lista'
        },
        function (data, textStatus) {
            if (data != 0 || data != "") {
                data = JSON.parse(data);
                $.each(data, function (index, fila) {
                    html_select += "<option value='" + fila.id + "'>" + fila.nombre + "</option>";
                });

                $("#lista").html(html_select).change();
            }
        });
}

function cargar_items(id_lista2) {
    html = '';

    $('#itemlist tbody').html('');

    var tabla = '<table id="itemlist" class="table dt-responsive nowrap" >'
        + '<thead>'
        + '<th align="center" class="all">Nombre SKU</th>'
        + '<th align="center">SKU</th>'
        + '<th align="center">Precio pdv</th>'
        + '</thead>'
        + '<tbody>'

        + '</tbody>'
        + '</table>'
        + '<input type="hidden" id="tipo_tip">'
        + '<input type="hidden" id="nom_lista">';

    $("#itemlistc").html(tabla);

    $.post('modulos/asignar_lista_v1.0.1/controlador.php',
        {
            accion: 'consultar_items',
            id_lista: id_lista2
        },
        function (data, textStatus) {
            data = JSON.parse(data);
            $.each(data, function (index, fila) {
                var precio_venta = fila.precio_pdv;
                var precioventa_directo = fila.precio_publico;
                if (precio_venta.indexOf('.') > 0) {
                    precio_venta = format(precio_venta, 2).replace(",", "");
                }
                if (precioventa_directo.indexOf('.') > 0) {
                    precioventa_directo = format(precioventa_directo, 2).replace(",", "");
                }
                html += '<tr><td>' + fila.producto + '</td><td>' + fila.pn + '</td><td>' + precio_venta + '</td></tr>';
            })
            $('#itemlist tbody').html(html);
            $("#itemlistc").show();
        })
}

function cerrar_moda_asig_list() {

    $('itemlist tbody').html('');
    $("#lista").val("");
}

function asignar_lista(asignacion_distri) {

    id_lista = $("#lista").val();
    nombre_lista = $("#lista option:selected").text();
    if (id_lista != "") {
        var mns_confirm = "";
        if (asignacion_distri == 1) {
            mns_confirm = "¿Esta seguro de realizar esta acción, recuerde que si lo hace serán quitadas las listas asignadas por los distribuidores y se asignara la seleccionada?";
        } else {
            mns_confirm = "¿Esta seguro de realizar esta acción?";
        }
        BootstrapDialog.confirm(mns_confirm, function (result) {
            if (result) {
                $.post('modulos/asignar_lista_v1.0.1/controlador.php',
                    {
                        accion: 'asignar_lista',
                        tipo2: 1,
                        id_tipo2: tipo_id,
                        id_lista2: id_lista,
                        distri_asignacion: asignacion_distri
                    },
                    function (data, textStatus) {
                        if (data != 0 || data != "") {
                            data = JSON.parse(data);
                            if (data.evento == 1) {
                                $('itemlist tbody').html('');
                                $("#lista").val("");
                                $(".modal").modal("hide");
                                mostrar_tabla_consulta();
                                Notificacion(data.msg, "success");
                            } else {
                                Notificacion(data.msg, "error");
                            }
                        }
                    });
            }
        });
    } else {
        Notificacion("Por favor seleccione una lista", "warning");
    }
}

function quitar_lista(id_lista, tipo_id1, tipo1) {

    $.post('modulos/asignar_lista_v1.0.1/controlador.php',
        {
            accion: 'quitar_lista',
            tipo2: tipo1,
            id_tipo2: tipo_id1,
            id_lista2: id_lista
        },
        function (data, textStatus) {
            if (data != 0 || data != "") {
                data = JSON.parse(data);
                if (data.evento == 1) {
                    Notificacion(data.msg, "success");
                    mostrar_tabla_consulta();
                } else {
                    Notificacion(data.msg, "error");
                }
            }
        });
}

<?php

require "../../config/mainModel.php";

class asignar_lista
{
    private $BD;

    public function __construct()
    {
        $BD = new BD();
        $this->BD = $BD;
        $this->BD->conectar();
    }
    public function __destruct()
    {
        $this->BD->desconectar();
    }

    public function traerRegionales()
    {
        $regionales = $this->BD->Permisos_Dcs_Regionales();

        $sql = "SELECT r.id, r.nombre FROM {$GLOBALS["BD_NAME"]}.regional r WHERE r.id IN ($regionales) GROUP BY r.id";

        return $this->BD->devolver_array($sql);
    }

    public function consultar_listas_items_distrib($id_lista)
    {
        $c_l_items = "SELECT l.nombre AS lista_n,
							 r.pn,r.producto,
							 il.precio_venta AS precio_pdv,
							 il.precioventa_directo AS precio_publico,
							 d.nombre AS distrib,
							 l.id AS id_lista
					    FROM {$GLOBALS["BD_NAME"]}.lista_precio l
				  INNER JOIN {$GLOBALS["BD_NAME"]}.items_lista il ON (l.id = il.id_lista)
				  INNER JOIN {$GLOBALS["BD_NAME"]}.distribuidores d ON (d.id = l.id_distribuidor)
				  INNER JOIN {$GLOBALS["BD_NAME"]}.referencias r ON (r.id = il.id_referencia)
					   WHERE l.id IN ($id_lista)";

        return $this->BD->devolver_array($c_l_items);
    }

    public function consulta_tabla($tipo, $id_tipo)
    {
        $regionales = $this->BD->Permisos_Dcs_Regionales();
        $sql = "";

        switch ($tipo) {

            case 0:

                $sql = "SELECT 'Regional' AS tipo,
								'1' AS tiponum,
								r.id AS tipo_id,
								r.nombre AS nombre_tipo,
								IF(lp.id_lista IS NULL,0,group_concat(lp.id_lista)) AS id_lista,
								IF(group_concat(CONCAT(IF(l.id_distribuidor = 0,'(Operador)','(Distribuidor)'),l.nombre)) IS NULL,'GENERAL',group_concat(CONCAT(IF(l.id_distribuidor = 0,'(Operador)','(Distribuidor)'),l.nombre))) AS nombre_lista,
								lp.estado,
								IF(group_concat(lp.id_distribuidor) IS NULL,'',group_concat(lp.id_distribuidor)) AS id_distribuidor,
								IF(l.estado_vigencia IS NULL,1,l.estado_vigencia) AS estado_vigencia
						   FROM {$GLOBALS["BD_NAME"]}.regional r
					  LEFT JOIN {$GLOBALS["BD_NAME"]}.niveles_listaprecios lp ON (lp.id_nivel=r.id and lp.tipo_nivel=1 and lp.estado=1)
					  LEFT JOIN {$GLOBALS["BD_NAME"]}.lista_precio l ON (l.id=lp.id_lista)
                      WHERE r.id IN ($regionales)
					   GROUP BY r.id";
                break;

            case 1:

                $sql = "SELECT 'Regional' AS tipo,
								'1' AS tiponum,
								r.id AS tipo_id,
								r.nombre AS nombre_tipo,
								IF(lp.id_lista IS NULL,0,group_concat(lp.id_lista)) AS id_lista,
								IF(group_concat(CONCAT(IF(l.id_distribuidor = 0,'(Operador)','(Distribuidor)'),l.nombre)) IS NULL,'GENERAL',group_concat(CONCAT(IF(l.id_distribuidor = 0,'(Operador)','(Distribuidor)'),l.nombre))) AS nombre_lista,
								lp.estado,
								group_concat(lp.id_distribuidor) AS id_distribuidor,
								IF(l.estado_vigencia IS NULL,1,l.estado_vigencia) AS estado_vigencia
						   FROM {$GLOBALS["BD_NAME"]}.regional r
					  LEFT JOIN {$GLOBALS["BD_NAME"]}.niveles_listaprecios lp ON (lp.id_nivel=r.id and lp.tipo_nivel=$tipo and lp.id_nivel in ($id_tipo) and lp.estado=1)
					  LEFT JOIN {$GLOBALS["BD_NAME"]}.lista_precio l ON (l.id=lp.id_lista)
						  WHERE r.id in ($id_tipo)
					   GROUP BY r.id";
                break;
        }

        return $this->BD->devolver_array($sql);
    }

    public function consultar_listas()
    {

        $consulta = "SELECT id, nombre FROM {$GLOBALS["BD_NAME"]}.lista_precio WHERE estado = 1 AND id_distribuidor = 0";

        return $this->BD->devolver_array($consulta);
    }

    public function consultar_items($id_lista)
    {
        $consulta = "SELECT lp.id,r.producto,r.pn,il.precio_venta AS precio_pdv,il.precioventa_directo AS precio_publico
					   FROM {$GLOBALS["BD_NAME"]}.items_lista il
				 INNER JOIN {$GLOBALS["BD_NAME"]}.lista_precio lp ON (lp.id = il.id_lista)
				 INNER JOIN {$GLOBALS["BD_NAME"]}.referencias r ON (r.id = il.id_referencia)
					  WHERE lp.id = $id_lista";

        $response = $this->BD->devolver_array($consulta);

        foreach ($response as $key => $value) {
            $response[$key]["producto"] = utf8_encode($response[$key]["producto"]);
        }

        return $response;
    }

    public function guardar_lista_tipo($tipo, $tipo_id, $id_lista, $distri_asignacion)
    {
        $oSession = unserialize($_SESSION[$GLOBALS["SESION_OP"]]);
        $id_user = $oSession->VSid;
        $error = 0;
        $id_l_or = 0;
        $id_reg = 0;

        //VALIDACION DE QUE NO EXISTA UNA ASIGNACION YA REALIZADA
        $c_asignacion = "SELECT id FROM {$GLOBALS["BD_NAME"]}.niveles_listaprecios WHERE tipo_nivel = $tipo AND id_nivel = $tipo_id AND id_distribuidor = 0 AND estado = 1;";
        $r_asignacion = $this->BD->devolver_array($c_asignacion);

        if (count($r_asignacion) > 0) {
            return array('evento' => -1, 'msg' => 'La asignacion ya se ha realizado.');
            exit();
        }

        //VALIDACION DE QUE NO EXISTA UNA ASIGNACION YA REALIZADA FIN

        $this->BD->consultar("BEGIN");

        $consulta = "SELECT id, IF(estado = 1,id_lista,0) AS id_lista FROM {$GLOBALS["BD_NAME"]}.niveles_listaprecios WHERE tipo_nivel = $tipo AND id_nivel = $tipo_id AND estado = 0 AND id_distribuidor = 0;";

        $datos = $this->BD->devolver_array($consulta);

        if (count($datos) > 0) {

            $id_l_or = $datos[0]["id_lista"];

            $sql = "UPDATE niveles_listaprecios SET estado = 1, id_lista = $id_lista, f_update = NOW() WHERE id = " . $datos[0]["id"] . ";";

            $sql_audi = "INSERT INTO audi_niveles_listaprecios(id_lista, tipo_nivel, id_nivel, estado, fecha, hora, usuario, accion) VALUES ($id_lista, $tipo, $tipo_id, 1, CURDATE(),CURTIME(), $id_user, 2);";

            if ($distri_asignacion == 1) {
                $u_asignacion_distrip = "UPDATE niveles_listaprecios SET estado = 0 WHERE tipo_nivel = $tipo AND id_nivel = $tipo_id AND id_distribuidor > 0;";
            }
        } else {

            $sql = "INSERT INTO niveles_listaprecios(tipo_nivel, id_nivel, id_lista, estado, f_insert) VALUES ($tipo, $tipo_id, $id_lista, 1, NOW());";

            $sql_audi = "INSERT INTO audi_niveles_listaprecios(id_lista, tipo_nivel, id_nivel, estado, fecha, hora, usuario, accion,id_distribuidor) VALUES ($id_lista, $tipo, $tipo_id, 1,CURDATE(), CURTIME(), $id_user, 1, 0);";

            if ($distri_asignacion == 1) {
                $u_asignacion_distrip = "UPDATE niveles_listaprecios SET estado = 0 WHERE tipo_nivel = $tipo AND id_nivel = $tipo_id AND id_distribuidor > 0;";
            }
        }

        $c_listap = "SELECT estado_vigencia FROM {$GLOBALS["BD_NAME"]}.lista_precio WHERE id = $id_lista;";
        $r_listap = $this->BD->devolver_array($c_listap);

        if ($r_listap[0]["estado_vigencia"] == 1) { //VALIDACION DE LA VIGENCIA DE LA LISTA DE PRECIOS
            //ACTUALIZACION DEL PADRE DE LA LISTA DE PRECIOS

            $c_distrib_lista = "SELECT id_distri FROM {$GLOBALS["BD_NAME"]}.regionales_distri WHERE id_reg = $tipo_id GROUP BY id_distri;";
            $r_distrib_lista = $datos = $this->BD->devolver_array($c_distrib_lista);
            $distrib = "";

            if (count($r_distrib_lista) > 0) {

                if (count($r_distrib_lista) > 1) {
                    foreach ($r_distrib_lista as $value) {
                        $c_distri_niv = "SELECT id FROM {$GLOBALS["BD_NAME"]}.niveles_listaprecios WHERE id_nivel IN (SELECT id_reg FROM {$GLOBALS["BD_NAME"]}.regionales_distri WHERE id_distri={$value["id_distri"]}) AND tipo_nivel = 1 AND estado = 1 AND id_distribuidor = 0;";

                        $r_distri_niv = $this->BD->devolver_array($c_distri_niv);
                        if (count($r_distri_niv) > 0) {
                            $distrib .= $value["id_distri"] . ",";
                        }
                    }

                    if ($distrib != "") {
                        $distrib = substr($distrib, 0, -1);
                    }
                } else {
                    $c_distri_niv = "SELECT id FROM {$GLOBALS["BD_NAME"]}.niveles_listaprecios WHERE id_nivel IN (SELECT id_reg FROM {$GLOBALS["BD_NAME"]}.regionales_distri WHERE id_distri = {$r_distrib_lista[0]["id_distri"]}) AND tipo_nivel = 1 AND estado = 1 AND id_distribuidor = 0;";

                    $r_distri_niv = $this->BD->devolver_array($c_distri_niv);
                    if (count($r_distri_niv) > 0) {
                        $distrib = $r_distrib_lista[0]["id_distri"];
                    }
                }
                if ($distrib != "") {
                    $u_origen_lista = "UPDATE {$GLOBALS["BD_NAME"]}.lista_precio SET id_lista_origen = $id_lista WHERE id_distribuidor IN ($distrib) AND id_lista_origen = $id_l_or ;";

                    $res = $this->BD->consultar($u_origen_lista);
                    if (!$res) {

                        $error = 1;
                    }
                }
            }
            //ACTUALIZACION DEL PADRE DE LA LISTA DE PRECIOS
        } //VALIDACION DE LA VIGENCIA DE LA LISTA DE PRECIOS FIN
        if ($r_listap[0]["estado_vigencia"] == 1) { //VALIDACION DE LA VIGENCIA DE LA LISTA DE PRECIOS
            if ($distri_asignacion == 1) {
                $res = $this->BD->consultar($u_asignacion_distrip);
                if (!$res) {

                    $error = 2;
                }
            }
        } //VALIDACION DE LA VIGENCIA DE LA LISTA DE PRECIOS FIN

        $res = $this->BD->consultar($sql);
        if (!$res) {

            $error = 3;
        }

        $res = $this->BD->consultar($sql_audi);
        if (!$res) {

            $error = 4;
        }

        $error = $this->BD->actualizar_listas_cascada($id_lista);

        if ($error == 0) {
            $this->BD->consultar("COMMIT");
            $to = "/topics/" . "regional_" . $tipo_id;
            $this->BD->notificar($to, '', '', 103);
            return array('evento' => 1, 'msg' => 'Se ha asignado la lista de precios con exito');
        } else {
            $this->BD->consultar("ROLLBACK");
            return array('evento' => -1, 'msg' => 'Error al intentar asignar la lista de precios');
        }
    }

    public function deshabilitar_asignacion_lista($tipo, $tipo_id, $id_lista)
    {
        $oSession = unserialize($_SESSION[$GLOBALS["SESION_OP"]]);
        $id_user = $oSession->VSid;
        $error = 0;
        $id_l_or = 0;

        $consulta = "SELECT nl.id,nl.id_lista
					   FROM {$GLOBALS["BD_NAME"]}.niveles_listaprecios nl
				 INNER JOIN {$GLOBALS["BD_NAME"]}.lista_precio lp ON (nl.id_lista=lp.id)
					   WHERE nl.tipo_nivel = $tipo AND nl.id_nivel = $tipo_id AND nl.id_lista IN ($id_lista) AND nl.estado=  1 AND lp.id_distribuidor = 0;";

        $datos = $this->BD->devolver_array($consulta);

        $this->BD->consultar("BEGIN");

        if (count($datos) > 0) {

            $id_l_or = $datos[0]["id_lista"];
            $sql = "UPDATE {$GLOBALS["BD_NAME"]}.niveles_listaprecios SET estado = 0, f_update = NOW() WHERE id = " . $datos[0]["id"] . ";";

            $sql_audi = "INSERT INTO {$GLOBALS["BD_NAME"]}.audi_niveles_listaprecios(id_lista, tipo_nivel, id_nivel, estado, fecha, hora, usuario, accion) VALUES ($id_lista, $tipo,$tipo_id, 0, CURDATE(), CURTIME(), $id_user, 2);";

            //ACTUALIZACION DEL PADRE DE LA LISTA DE PRECIOS
            $c_distrib_lista = "SELECT id_distri FROM {$GLOBALS["BD_NAME"]}.regionales_distri WHERE id_reg = $tipo_id GROUP BY id_distri;";
            $r_distrib_lista = $datos = $this->BD->devolver_array($c_distrib_lista);
            $distrib = "";
            if (count($r_distrib_lista) > 0) {

                if (count($r_distrib_lista) > 1) {
                    foreach ($r_distrib_lista as $value) {
                        $c_distri_niv = "SELECT id FROM {$GLOBALS["BD_NAME"]}.niveles_listaprecios WHERE id_nivel IN (SELECT id_reg FROM {$GLOBALS["BD_NAME"]}.regionales_distri WHERE id_distri = {$value["id_distri"]}) AND tipo_nivel = 1 AND estado = 1;";
                        $r_distri_niv = $this->BD->devolver_array($c_distri_niv);

                        if (count($r_distri_niv) > 0) {
                            $distrib .= $value["id_distri"] . ",";
                        }
                    }
                    if ($distrib != "") {
                        $distrib = substr($distrib, 0, -1);
                    }
                } else {
                    $c_distri_niv = "SELECT id FROM {$GLOBALS["BD_NAME"]}.niveles_listaprecios WHERE id_nivel IN (SELECT id_reg FROM {$GLOBALS["BD_NAME"]}.regionales_distri WHERE id_distri = {$r_distrib_lista[0]["id_distri"]}) AND tipo_nivel = 1 AND estado = 1;";
                    $r_distri_niv = $this->BD->devolver_array($c_distri_niv);

                    if (count($r_distri_niv) > 0) {
                        $distrib = $r_distrib_lista[0]["id_distri"];
                    }
                }
                if ($distrib != "") {
                    $u_origen_lista = "UPDATE {$GLOBALS["BD_NAME"]}.lista_precio SET id_lista_origen = 0 WHERE id_distribuidor IN ($distrib) AND id_lista_origen = $id_l_or ;";

                    $res = $this->BD->consultar($u_origen_lista);
                    if (!$res) {
                        $error = 1;
                    }
                }
            }
            //ACTUALIZACION DEL PADRE DE LA LISTA DE PRECIOS

            $res = $this->BD->consultar($sql);

            if (!$res) {
                $error = 1;
            }

            $res = $this->BD->consultar($sql_audi);

            if (!$res) {
                $error = 1;
            }

            $error = $this->BD->actualizar_listas_cascada(0);
        }

        if ($error != 1) {
            $this->BD->consultar("COMMIT");
            $to = "/topics/" . "regional_" . $tipo_id;
            $this->BD->notificar($to, '', '', 103);
            return array('evento' => 1, 'msg' => 'Se ha quitado la lista de precios con exito');
        } else {
            $this->BD->consultar("ROLLBACK");
            return array('evento' => -1, 'msg' => 'Error al intentar quitar la lista de precios');
        }
    }

    function traerCanales()
    {
        return array();
    }

    function traerDistribuidores()
    {
        return array();
    }
}

// Fin clase

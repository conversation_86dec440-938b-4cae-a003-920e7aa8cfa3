<?php
require("../../config/mainModel.php");
class asignar_bodega
{
  /*Metodos de la DB.
    $this->consultar_datos($query); // Realiza la consulta y devuelve un Array Asociativo con los datos
    $this->ejecuta_query($query); // Ejecuta Insert, Update, Delete, Alter, Drop.. / devuelve el ultimo id afectado.
    $this->total_registros($columna, $tabla, $where = ""); // Devuelve el numero de registros. count();
    $this->trae_uno($query); // Realiza una consulta, devolviendo el primer valor que encuentre.
    $this->begin_work();
    $this->commit();
    $this->rollback(); /////
  */
  private $DB;

  public function __construct()
  {
    $BD = new BD();
    $this->DB = $BD;
    $this->DB->conectar();
  }
  public function __destruct()
  {
    $this->DB->desconectar();
  }

  //Espacio Para declara las funciones que retornan los datos de la DB.
  public function Consultar_Distri($datos)
  {
    $sql = "";
    if ($datos["distribuidor"] != "") {
      $sql .= "AND b.id_dependencia in (SELECT id FROM distribuidores  WHERE id = '" . $datos["distribuidor"] . "') ";
    }

    if ($datos["bodega"] != "") {
      $sql .= "AND b.id = '" . $datos["bodega"] . "' ";
    }

    if ($datos["cod_bodega"] != "") {
      $sql .= "AND b.codigo_bodega = '" . $datos["cod_bodega"] . "'";
    }

    if ($datos["cod_logist"] != "") {
      $sql .= "AND b.cod_logistica = '" . $datos["cod_logist"] . "'";
    }

    if ($datos["regional"] != "") {
      $sql .= "AND b.id_regdistri = '" . $datos["regional"] . "'";
    }

    $consulta = "SELECT b.id,b.codigo_bodega,b.nombre_bodega, d.nombre depto, m.nombre ciudad,id_dependencia,id_regdistri, cod_logistica,
                  (SELECT nombre FROM distribuidores WHERE id = b.id_dependencia) distri,
                  (SELECT nombre FROM regional WHERE id = b.id_regdistri) regional
                  FROM bodega b, departamentos d, municipios m
                  WHERE b.departamento = d.id AND b.ciudad = m.id $sql";

    return $this->DB->devolver_array($consulta);
  }
  public function Retornar_Regionales($distri)
  {
    $consulta = "SELECT r.id, r.nombre FROM regionales_distri rd
                    INNER JOIN regional r ON r.id = rd.id_reg
                    INNER JOIN distribuidores d ON d.id = rd.id_distri
                    WHERE d.id ='$distri' and r.estado = 1";
    return $this->DB->devolver_array($consulta);
  }
  public function Asignar_Bodega($datos)
  {
    $oSession = unserialize($_SESSION[$GLOBALS["SESION_OP"]]);
    $id = $oSession->VSid;

    $response = array();
    $tipo_dependencia = 0;
    if ($datos['new_distri'] != "0") {
      $tipo_dependencia = 1;
    }

    $this->DB->consultar("BEGIN");

    //Validación cantidad de productos en Bodega
    $val_pro = "SELECT COUNT(id) AS cant_pro FROM producto WHERE aceptado = 1 AND id_bodega = " . $datos['id'];
    $res_pro = $this->DB->consultar($val_pro);
    $cant_pro_bod = $res_pro->fields['cant_pro'];

    //Validación cantidad de simcards en Bodega
    $val_sims = "SELECT COUNT(id) AS cant_sims FROM simcards WHERE aceptado = 1 AND id_bodega = " . $datos['id'];
    $res_sims = $this->DB->consultar($val_sims);
    $cant_sims_bod = $res_sims->fields['cant_sims'];

    if ($cant_pro_bod != 0 || $cant_sims_bod != 0) {
      $response = array('res' => 2, 'msg' => 'Esta bodega contiene producto en conflicto comuniquese con soporte.');
    } else {
      $error = false;
      $update2 = "UPDATE bodega SET tipo_dependencia = '$tipo_dependencia', 
                                        id_dependencia = '" . $datos['new_distri'] . "', 
                                        id_regdistri = '" . $datos['new_regional'] . "' 
                      WHERE id = '" . $datos['id'] . "'";

      if (!$this->DB->consultar($update2)) {
        $error = true;
      }

      $update_simcards = "UPDATE `simcards` SET distri = '" . $datos['new_distri'] . "' WHERE `distri` = 0 AND bodega = '" . $datos['codigo_bodega'] . "'";

      if (!$this->DB->consultar($update_simcards)) {
        $error = true;
      }

      $update_equipos = "UPDATE `producto` SET distri = '" . $datos['new_distri'] . "' WHERE `distri` = 0 AND bodega = '" . $datos['codigo_bodega'] . "'";

      if (!$this->DB->consultar($update_equipos)) {
        $error = true;
      }

      $sql3 = "insert into audi_bodega (distri,regional,fecha,hora,usuario,id_bodega,movimiento) values (" . $datos['new_distri'] . "," . $datos['new_regional'] . ",curdate(),curtime(),$id,'" . $datos['id'] . "',2)";

      $redaudi = $this->DB->consultar($sql3);
      if (!$redaudi) {
        $error = true;
      }

      if (!$error) {
        $response = array('res' => 1, 'msg' => 'Se realizo la asignacion con exito.');
        $this->DB->consultar("COMMIT");
      } else {
        $response = array('res' => -1, 'msg' => 'Error no se puede realizar la asignacion.');
        $this->DB->consultar("ROLLBACK");
      }
    }

    return $response;
  }

  public function Comprobar_Iguales($dato, $id = 0)
  {
    $res = true;
    $dato = trim(strtoupper($dato));
    $bodegas = $this->Retornar_Bodegas();
    for ($i = 0; $i < count($bodegas); $i++) {
      $nombre = trim(strtoupper($bodegas[$i]['nombre_bodega']));
      $id_ = $bodegas[$i]['id_bodega'];
      if ($id != 0) {
        if ($nombre == $dato && $id != $id_) {
          $res = false;
        }
      } else {
        if ($nombre == $dato) {
          $res = false;
        }
      }
    }
    return $res;
  }

  public function Retornar_Bodegas($tipo = "")
  {
    $sql = "";
    if ($tipo != "") {
      $sql .= "AND tipo_dependencia = 0";
    }

    $consulta = "SELECT id warehouse_id, nombre_bodega name FROM bodega WHERE estado = 1 $sql order by 2";

    return $this->DB->devolver_array($consulta);
  }

  public function Retornar_Cantidad_Produc($codigo_bodega)
  {
    //CANTIDAD SIMCARDS DISPONIBLES PARA BODEGA SELECCIONADA
    $sql_simcards = "SELECT count(id) as cantidad FROM `simcards` WHERE `bodega` = '$codigo_bodega' AND `distri` = 0;";
    $rsp_c = $this->DB->devolver_array($sql_simcards);

    //CANTIDAD EQUIPOS DISPONIBLES PARA BODEGA SELECCIONADA
    $sql_productos = "SELECT count(id) as cantidad FROM `producto` WHERE `bodega` = '$codigo_bodega' AND `distri` = 0;";
    $rsp_p = $this->DB->devolver_array($sql_productos);

    $array_cantidad = [];

    if (!empty($rsp_c)) {
      $array_cantidad["simcards"] = number_format($rsp_c[0]["cantidad"], 0, '.', '.');
    }

    if (!empty($rsp_p)) {
      $array_cantidad["equipos"] = number_format($rsp_p[0]["cantidad"], 0, '.', '.');
    }

    return $array_cantidad;
  }
} // Fin clase

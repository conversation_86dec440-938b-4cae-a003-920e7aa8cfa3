const URL_MODULO = 'modulos/asignar_bodega_v1.0.0/';
const DEFAULT_OPTION = "<option value='0'>SELECCIONAR</option>";
var dtable;
var d_bodega;

$(document).ready(function () {
	funcion_cargar_regionales("regional");
	Funcion_Cargar_Bodegas("bodega");
	funcion_cargar_distribuidores("distribuidor");
	$("#btn_crear").click(function () {
		tipo_crud = 1;
		Cargar_Departamento_Ciudad("crud_depto", "crud_ciudad");
		$("#frm_crud")[0].reset();
		$("#crud_bodega").val("");
		$(".modal").modal();
	})
	$("#frm_busqueda").submit(function (event) {
		event.preventDefault();
		Cargar_Tabla_Asigna();
	});
	$("#frm_crud").submit(function (event) {
		event.preventDefault();
		if ($("#cr_distri").val() == "0" || $("#cr_regional").val() == "0") {
			BootstrapDialog.confirm("¿Esta seguro de eliminar la asignacion de la bodega?", function (result) {
				if (result) {
					Asignar_Bodega();
				}
			});
		}
		else {
			Asignar_Bodega();
		}

	})
	$("#cr_distri").change(function () {
		if (d_bodega && $(this).val().length > 0) {
			Cargar_Regionales_Distri($(this).val(), function (data) {
				$("#cr_regional").val('0').change();
				$.each(data, function (index, fila) {
					if (fila.id == d_bodega['id_regdistri']) {
						$("#cr_regional").val(fila.id).change();
					}
				});
			});
		} else {
			$("#cr_regional").html(DEFAULT_OPTION).change();
			$("#cr_regional").val('0').change();
		}
	})
});
function Cargar_Tabla_Asigna() {
	if (!$.fn.DataTable.isDataTable('#t_asig_bodega')) {
		dtable = $("#t_asig_bodega").DataTable({
			ajax: function (data, callback, settings) {
				fetch(`${URL_MODULO}/controlador.php`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						accion: "Consultar_Distri",
						datos: formToJSON("#frm_busqueda")
					})
				}).then((response) => response.json()).then((response) => {
					callback({
						draw: response.draw,
						recordsTotal: response.recordsTotal,
						recordsFiltered: response.recordsFiltered,
						data: response.data
					});
				}).catch((error) => {
					console.error(error);
				});
			},
			"bFilter": false,
			"responsive": true,
			"columns": [
				{ "data": "codigo_bodega" },
				{ "data": "nombre_bodega" },
				{ "data": "cod_logistica" },
				{ "data": "depto" },
				{ "data": "ciudad" },
				{ "data": "distri" },
				{ "data": "regional" },
				{ "data": "regional" }
			],
			"columnDefs": [
				{
					"targets": 7,
					"data": "",
					render: function (data, type, row) {
						return '<button class="btn btn-sm btn-primary asign tproducto"><i class="glyphicon glyphicon-pushpin"></i></button>';
					}
				}
			],
			fnDrawCallback: function (var1, var2, var3) {
				$(".asign").unbind("click");
				$(".asign").click(function () {
					d_bodega = dtable.row($(this).parents('tr')).data();
					cargarProductos(d_bodega['codigo_bodega']);
					$("#name_bodega").html(d_bodega['nombre_bodega']);
					$(".modal").modal("show");
					funcion_cargar_distribuidores("cr_distri", "", function () {
						$("#cr_distri").val(d_bodega['id_dependencia']).change();
					});
				})
			}

		});
	}
	else {
		dtable.destroy();
		Cargar_Tabla_Asigna();
	}
	$("#resultados").show();
}

function Cargar_Regionales_Distri(distri, callback = false) {
	fetch(`${URL_MODULO}/controlador.php`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			accion: 'Retornar_Regionales',
			distri: distri
		})
	}).then(response => response.json()).then(data => {
		if (data != 0 || data != "") {
			var html_regional = DEFAULT_OPTION;
			$.each(data, function (index, fila) {
				html_regional += "<option value='" + fila.id + "'>" + fila.nombre + "</option>"
			});
			$("#cr_regional").html(html_regional);
			if (callback) {
				callback(data);
			}
		}
	}).catch(error => {
		console.error('Error:', error);
	});
}

function Asignar_Bodega(datos) {
	d_bodega.new_distri = $("#cr_distri").val();
	d_bodega.new_regional = $("#cr_regional").val();
	fetch(`${URL_MODULO}/controlador.php`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			accion: 'Asignar_Bodega',
			datos: d_bodega
		})
	}).then(response => response.json()).then(data => {
		if (data['res'] == 1) {
			Notificacion(data['msg'], "success");
			$(".modal").modal("hide");
			Cargar_Tabla_Asigna();
		} else if (data['res'] == -1) {
			Notificacion(data['msg'], "error");
		} else if (data['res'] == 2) {
			Notificacion(data['msg'], "error");
		}
		else {
			Notificacion("Error al intentar enviar los datos", "error");
		}
	}).catch(error => {
		console.error('Error:', error);
	});
}

function cargarProductos(codigo_bodega) {
	var tabla = "";
	$('#tabla_resultados').html('<div class="loading" style="text-align: center"><br/>Un momento, por favor...</div>');
	fetch(`${URL_MODULO}/controlador.php`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			accion: 'Retornar_Cantidad_Produc',
			codigo_bodega: codigo_bodega
		})
	}).then(response => response.json()).then(data => {
		tabla += '<table id="t_cantidad_p" class="table table-hover table-striped dt-responsive nowrap" cellspacing="0" width="100%">';
		tabla += '<thead><tr><th style="text-align: center">TIPO</th><th style="text-align: center">CANTIDAD</th></tr></thead>';
		var x = 0;

		tabla += '<tr class="cantidad">';
		if (data.simcards > 0) {
			tabla += '<td style="text-align: center">SIMCARDS</td>';
			tabla += '<td style="text-align: center">' + data.simcards + '</td>';
			x = 1;
		}

		if (data.equipos > 0) {
			tabla += '<td style="text-align: center">EQUIPOS</td>';
			tabla += '<td style="text-align: center">' + data.equipos + '</td>';
			x = 1;
		}

		if (x == 0) {
			tabla += '<td style="text-align: center" colspan="2">NO HAY REGISTROS</td>';
		}

		tabla += '</tr>';
		tabla += "</table>"
		$("#tabla_resultados").html(tabla);
	}).catch(error => {
		console.error('Error:', error);
	});
}
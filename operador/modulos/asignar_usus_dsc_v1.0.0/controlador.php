<?php
include_once("../../config/mainController.php");
 // Incluye el Controlador Principal
include_once("../../modulos/asignar_usus_dsc_v1.0.0/modelo.php");

$controller = new mainController; // Instancia a la clase MainController
$aslist = new asignar_dcs();
try // Try, manejo de Errores
{
	$tipo_res = "";
	$response = null; 
	// Se manejaran dos tipos JSON y HTML
	// Dependiendo del método de la petición ejecutaremos la acción correspondiente.
	// Por ahora solo POST, todas las llamadas se haran por POST
    $variables = $_POST;
	if(!isset($_POST['accion'])){echo "0"; return;} // Evita que ocurra un error si no manda accion.
		$accion = $variables['accion'];
		// Dependiendo de la accion se ejecutaran las tareas y se definira el tipo de respuesta.
		
		switch($accion) {
			case 'mostrar_regionales': 
				 $tipo_res = 'JSON';
				 $cedula = $variables['cedula'];
				 $usuario = $variables['usuario'];
				 $array = $aslist->mostrar_regionales($cedula,$usuario);
				 $response = array("draw" => 1,"recordsTotal" => 0,"recordsFiltered" => 0,"data" => $array);
			break;
			case 'mostrar_canales': 
				 $tipo_res = 'JSON';
				 $regional = $variables['regional'];
				 $cedula = $variables['cedula'];
				 $usuario = $variables['usuario'];
				 $array = $aslist->mostrar_canales($regional,$cedula,$usuario);
				 $response = array("draw" => 1,"recordsTotal" => 0,"recordsFiltered" => 0,"data" => $array);
			break;
			case 'mostrar_distribuidores': 
				 $tipo_res = 'JSON';
				 $regional = $variables['regional'];
				 $canal = $variables['canal'];
				 $cedula = $variables['cedula'];
				 $usuario = $variables['usuario'];
				 $array = $aslist->mostrar_distribuidores($canal,$regional,$cedula,$usuario);
				 $response = array("draw" => 1,"recordsTotal" => 0,"recordsFiltered" => 0,"data" => $array);
			break;	
			case 'mostrar_territorios':
				 $tipo_res = 'JSON';
				 $id_regional = $variables['regional'];
				 $id_canal = $variables['canal'];
				 $id_distri = $variables['distri'];
				 $cedula = $variables['cedula'];
				 $usuario = $variables['usuario'];
				 $datos = $aslist->mostrar_territorios($id_regional,$id_canal, $id_distri,$cedula,$usuario);
				 $response = array("draw" => 1, "recordsTotal" => 0, "recordsFiltered" => 0,"data" => $datos);
			break;
			case 'mostrar_zonas': 
				 $tipo_res = 'JSON';
				 $regional = $variables['regional'];
				 $canal = $variables['canal'];
				 $distri = $variables['distri'];
				 $territorio = $variables['territorio'];
		 		 $cedula = $variables['cedula'];
				 $usuario = $variables['usuario'];
				 $array = $aslist->mostrar_zonas($regional,$canal,$distri,$territorio,$cedula,$usuario);
				  $response = array("draw" => 1,"recordsTotal" => 0,"recordsFiltered" => 0,"data" => $array);
			break;
			case 'Mostrar_Arbol':
				 $cedulaus = $variables['cc'];
				 $tipo_res = 'JSON';
				 $response = $aslist->Retornar_Arbol($cedulaus);
			break;
			case 'mostrar_puntos':
				 $tipo_res = 'JSON';
				 $regional = $variables['regional'];
				 $canal = $variables['canal'];
				 $distri = $variables['distri'];
				 $territorio = $variables['territorio'];
				 $zona = $variables['zona'];
		 		 $cedula = $variables['cedula'];
				 $usuario = $variables['usuario'];
				 $datos = $aslist->Mostrar_Puntos($regional,$canal,$distri,$territorio,$zona,$cedula,$usuario);
				 $response = array("draw" => 1,"recordsTotal" => 0,"recordsFiltered" => 0,"data" => $datos);
			break;
			case 'Guardar_Permisos':
				 $tipo_res = 'JSON';
				 $tipo = $variables['tipo'];
				 $id = $variables['id'];
				 $cedula = $variables['cedula'];
				 $usuario = $variables['usuario'];
				 $aslist->audi_paralela($tipo,$id,$cedula,$usuario,1);
				 $response = $aslist->Guardar_Permisos($tipo,$id,$cedula,$usuario);
			break;
			case 'Borrar_Permisos':
				 $tipo_res = 'JSON';
				 $tipo = $variables['tipo'];
				 $id = $variables['id'];
				 $cedula = $variables['cedula'];
				 $usuario = $variables['usuario'];
				 $aslist->audi_paralela($tipo,$id,$cedula,$usuario,2);
				 $response = $aslist->Borrar_Permisos($tipo,$id,$cedula,$usuario);
			break;

			//Territorios
			case 'Borrar_Permisos_Territorio':
				 $tipo_res = 'JSON';
				 $regional = $variables['regional'];
				 $canal = $variables['canal'];
				 $distri = $variables['distri'];
				 $id_terr = $variables['id_terr'];
				 $cedula = $variables['cedula'];
				 $usuario = $variables['usuario'];
				 $aslist->audi_paralela_territorio($regional,$canal,$distri,$id_terr,$usuario,2);
				 $response = $aslist->Borrar_Permisos_Territorio($regional,$canal,$distri,$id_terr,$cedula,$usuario);
			break;
			case 'Guardar_Permisos_Territorio':
				 $tipo_res = 'JSON';
				 $regional = $variables['regional'];
				 $canal = $variables['canal'];
				 $distri = $variables['distri'];
				 $id_terr = $variables['id_terr'];
				 $cedula = $variables['cedula'];
				 $usuario = $variables['usuario'];
				 $aslist->audi_paralela_territorio($regional,$canal,$distri,$id_terr,$usuario,1);
				 $response = $aslist->Guardar_Permisos_Territorio($regional,$canal,$distri,$id_terr,$cedula,$usuario);
			break;
			/*-------------------------*/
			//Zonas
			case 'Borrar_Permisos_Zonas':
				 $tipo_res = 'JSON';
				 $regional = $variables['regional'];
				 $canal = $variables['canal'];
				 $distri = $variables['distri'];
				 $id_terr = $variables['territorio'];
				 $zona = $variables['zona'];
				 $cedula = $variables['cedula'];
				 $usuario = $variables['usuario'];
				 $aslist->audi_paralela_zonas($regional,$canal,$distri,$id_terr,$zona,$usuario,2);
				 $response = $aslist->Borrar_Permisos_Zonas($regional,$canal,$distri,$id_terr,$zona,$cedula,$usuario);
			break;
			case 'Guardar_Permisos_Zonas':
				 $tipo_res = 'JSON';
				 $regional = $variables['regional'];
				 $canal = $variables['canal'];
				 $distri = $variables['distri'];
				 $id_terr = $variables['territorio'];
				 $zona = $variables['zona'];
				 $cedula = $variables['cedula'];
				 $usuario = $variables['usuario'];
				 $aslist->audi_paralela_zonas($regional,$canal,$distri,$id_terr,$zona,$usuario,1);
				 $response = $aslist->Guardar_Permisos_Zonas($regional,$canal,$distri,$id_terr,$zona,$cedula,$usuario);
			break;
			/*-------------------------*/
			//Puntos
			case 'Borrar_Permisos_Puntos':
				 $tipo_res = 'JSON';
				 $regional = $variables['regional'];
				 $canal = $variables['canal'];
				 $distri = $variables['distri'];
				 $id_terr = $variables['territorio'];
				 $zona = $variables['zona'];
				 $punto = $variables['punto'];
				 $cedula = $variables['cedula'];
				 $usuario = $variables['usuario'];
				 $response = $aslist->Borrar_Permisos_Puntos($regional,$canal,$distri,$id_terr,$zona,$punto,$cedula,$usuario);
			break;
			case 'Guardar_Permisos_Puntos':
				 $tipo_res = 'JSON';
				 $regional = $variables['regional'];
				 $canal = $variables['canal'];
				 $distri = $variables['distri'];
				 $id_terr = $variables['territorio'];
				 $zona = $variables['zona'];
				 $punto = $variables['punto'];
				 $cedula = $variables['cedula'];
				 $usuario = $variables['usuario'];
				 $response = $aslist->Guardar_Permisos_Puntos($regional,$canal,$distri,$id_terr,$zona,$punto,$cedula,$usuario);
			break;
			/*-------------------------*/
			case 'Mostrar_Usuario':
				$tipo_res = "JSON";
				$cedula = $variables['cedula'];
				$usuario = $variables['usuario'];
				$response = $aslist->Retornar_Usuario($cedula,$usuario);
			break;
		}

	// Respuestas del Controlador
	if($tipo_res == "JSON")
	{  
	  echo json_encode($response,true); // $response será un array con los datos de nuestra respuesta.
	}
	elseif ($tipo_res == "HTML") {
	  echo $response; // $response será un html con el string de nuestra respuesta.
	}
} // Fin Try
catch (Exception $e) {}

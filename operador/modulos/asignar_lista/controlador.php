<?php
include_once("../../config/mainController.php");
include_once("./modelo.php");

$controller = new mainController(); // Instancia a la clase MainController
$modelo = new asignar_lista();
try // Try, manejo de Errores
{
	$tipo_res = "";
	$response = null;
	// Se manejaran dos tipos JSON y HTML
	// Dependiendo del método de la petición ejecutaremos la acción correspondiente.
	// Por ahora solo POST, todas las llamadas se haran por POST
	$variables = $_POST;
	if (!isset($_POST['accion'])) {
		echo "0";
		return;
	} // Evita que ocurra un error si no manda accion.
	$accion = $variables['accion'];
	// Dependiendo de la accion se ejecutaran las tareas y se definira el tipo de respuesta.
	switch ($accion) {
		case 'consulta_tabla':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$tipo = $variables['tipo2'];
			$id_tipo = $variables['id_tipo2'];
			$datos = $modelo->consulta_tabla($tipo, $id_tipo);
			$response = array(
				"draw" => 0,
				"recordsTotal" => 0,
				"recordsFiltered" => 0,
				"data" => $datos
			);


			break;

		case 'consultar_listas_items_distrib':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$id_lista = $variables["id_lista"];
			$response = $modelo->consultar_listas_items_distrib($id_lista);
			break;
		case 'consultar_lista':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$response = $modelo->consultar_listas();
			break;
		case 'asignar_lista':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$tipo = $variables['tipo2'];
			$tipo_id = $variables['id_tipo2'];
			$id_lista = $variables['id_lista2'];
			$distri_asignacion = $variables['distri_asignacion'];
			$response = $modelo->guardar_lista_tipo($tipo, $tipo_id, $id_lista, $distri_asignacion);
			break;
		case 'quitar_lista':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$tipo = $variables['tipo2'];
			$tipo_id = $variables['id_tipo2'];
			$id_lista = $variables['id_lista2'];
			$response = $modelo->deshabilitar_asignacion_lista($tipo, $tipo_id, $id_lista);
			break;
		case 'consultar_items':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$id_lista = $variables['id_lista'];
			$response = $modelo->consultar_items($id_lista);
			//$response = array("draw" =>0,
			//			    "recordsTotal" => 0,
			//			    "recordsFiltered" => 0,
			//			    "data" => $datos);
			break;

		case 'traerCanales':
			$tipo_res = "JSON";
			$response = $modelo->traerCanales();
			break;
		case 'traerDistribuidores':
			$tipo_res = "JSON";
			$regional = $variables["regional"];
			$response = $modelo->traerDistribuidores($regional);
			break;
		case 'traerRegionales':
			$tipo_res = "JSON";
			$response = $modelo->traerRegionales();
			break;
	}

	// Respuestas del Controlador
	if ($tipo_res == "JSON") {
		echo json_encode($response, true); // $response será un array con los datos de nuestra respuesta.
	} elseif ($tipo_res == "HTML") {
		echo $response; // $response será un html con el string de nuestra respuesta.
	}
} // Fin Try
catch (Exception $e) {
}

<?php
include_once("../../config/mainController.php");
include_once("./modelo.php");

$controller = new mainController(); // Instancia a la clase MainController
$modelo = new asignar_lista();
try // Try, manejo de Errores
{
	$metodo = $_SERVER['REQUEST_METHOD'];
	$tipo_res = "";
	$response = null;
	$variables = array();

	if (!empty($_POST)) {
		$variables = $_POST;
	} else if ($metodo == 'POST' && $_SERVER['CONTENT_TYPE'] == 'application/json') {
		// leer json
		$json = file_get_contents('php://input');
		$variables = json_decode($json, true);
	}

	// Evita que ocurra un error si no manda accion.
	if (!isset($variables['accion'])) {
		http_response_code(404);
		header("Content-type: text/plain; charset=utf-8");
		echo "0";
		return;
	}

	$accion = $variables['accion'];
	// Dependiendo de la accion se ejecutaran las tareas y se definira el tipo de respuesta.
	switch ($accion) {
		case 'consulta_tabla':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$tipo = $variables['tipo2'];
			$id_tipo = $variables['id_tipo2'];
			$datos = $modelo->consulta_tabla($tipo, $id_tipo);
			$response = array(
				"draw" => 0,
				"recordsTotal" => 0,
				"recordsFiltered" => 0,
				"data" => $datos
			);
			break;
		case 'consultar_listas_items_distrib':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$id_lista = $variables["id_lista"];
			$response = $modelo->consultar_listas_items_distrib($id_lista);
			break;
		case 'consultar_lista':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$response = $modelo->consultar_listas();
			break;
		case 'asignar_lista':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$tipo = $variables['tipo2'];
			$tipo_id = $variables['id_tipo2'];
			$id_lista = $variables['id_lista2'];
			$distri_asignacion = $variables['distri_asignacion'];
			$response = $modelo->guardar_lista_tipo($tipo, $tipo_id, $id_lista, $distri_asignacion);
			break;
		case 'quitar_lista':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$tipo = $variables['tipo2'];
			$tipo_id = $variables['id_tipo2'];
			$id_lista = $variables['id_lista2'];
			$response = $modelo->deshabilitar_asignacion_lista($tipo, $tipo_id, $id_lista);
			break;
		case 'consultar_items':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$id_lista = $variables['id_lista'];
			$response = $modelo->consultar_items($id_lista);
			//$response = array("draw" =>0,
			//			    "recordsTotal" => 0,
			//			    "recordsFiltered" => 0,
			//			    "data" => $datos);
			break;
		case 'traerCanales':
			$tipo_res = "JSON";
			$response = $modelo->traerCanales();
			break;
		case 'traerDistribuidores':
			$tipo_res = "JSON";
			$regional = $variables["regional"];
			$response = $modelo->traerDistribuidores($regional);
			break;
		case 'traerRegionales':
			$tipo_res = "JSON";
			$response = $modelo->traerRegionales();
			break;
		default:
			http_response_code(404);
			header("Content-type: text/plain; charset=utf-8");
			echo "0";
			return;
	}

	// Respuestas del Controlador
	if ($tipo_res == "JSON") {
		header("Content-type: application/json; charset=utf-8");
		$json_output = json_encode($response, true); // $response será un array con los datos de nuestra respuesta.
		if (json_last_error() !== JSON_ERROR_NONE) {
			// Si hubo un error en la codificación JSON
			throw new Exception("Error al codificar la respuesta JSON: " . json_last_error_msg());
		} else {
			echo $json_output;
		}
	} elseif ($tipo_res == "HTML") {
		header("Content-type: text/html; charset=utf-8");
		echo $response; // $response será un html con el string de nuestra respuesta.
	} else {
		header("Content-type: text/plain; charset=utf-8");
		echo $response; // $response será un texto plano con el string de nuestra respuesta.
	}
} // Fin Try
catch (Exception $e) {
	header("Content-type: application/json; charset=utf-8");
	http_response_code(500);
	echo json_encode(array("error" => true, "mensaje" => $e->getMessage()));
} catch (Error $e) {
	header("Content-type: application/json; charset=utf-8");
	http_response_code(500);
	echo json_encode(array("error" => true, "mensaje" => $e->getMessage()));
}

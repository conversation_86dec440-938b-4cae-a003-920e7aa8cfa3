<?php

require("../../config/mainModel.php");

class asignar_lista
{
	private $DB;

	public function __construct()
	{
		$BD = new BD();
		$this->DB = $BD;
		$this->DB->conectar();
	}
	public function __destruct()
	{
		$this->DB->desconectar();
	}


	public function traerRegionales()
	{
		$regionales = $this->DB->Permisos_Dcs_Regionales();
		$sql = "SELECT r.id, r.nombre FROM {$GLOBALS["BD_NAME"]}.regional r
    			WHERE r.id IN ($regionales) GROUP BY r.id";
		$res = $this->DB->devolver_array($sql);
		return $res;
	}

	public function consultar_listas_items_distrib($id_lista)
	{
		$c_l_items = "SELECT l.nombre as lista_n,r.pn,r.producto,il.precio_venta as precio_pdv,il.precioventa_directo as precio_publico,d.nombre as distrib,l.id as id_lista
						FROM {$GLOBALS["BD_NAME"]}.lista_precio l
						INNER JOIN {$GLOBALS["BD_NAME"]}.items_lista il ON (l.id = il.id_lista)
						INNER JOIN {$GLOBALS["BD_NAME"]}.distribuidores d ON (d.id = l.id_distribuidor)
						INNER JOIN {$GLOBALS["BD_NAME"]}.referencias r ON (r.id = il.id_referencia)
						WHERE l.id IN ($id_lista);";
		$res = $this->DB->devolver_array($c_l_items);
		return $res;
	}

	public function consulta_tabla($tipo, $id_tipo)
	{
		$sql = "";
		switch ($tipo) {
			case 0:
				$sql = "SELECT 'Regional' as tipo,'1' as tiponum,r.id as tipo_id,r.nombre as nombre_tipo,if(lp.id_lista is null,0,group_concat(lp.id_lista))as id_lista,if(group_concat(concat(if(l.id_distribuidor=0,'(Operador)','(Distribuidor)'),l.nombre))is null,'GENERAL',group_concat(concat(if(l.id_distribuidor=0,'(Operador)','(Distribuidor)'),l.nombre))) as nombre_lista,lp.estado,if(group_concat(lp.id_distribuidor)is null,'',group_concat(lp.id_distribuidor)) as id_distribuidor,if(l.estado_vigencia is null,1,l.estado_vigencia) as estado_vigencia
						FROM {$GLOBALS["BD_NAME"]}.regional r
						LEFT JOIN  {$GLOBALS["BD_NAME"]}.niveles_listaprecios lp ON (lp.id_nivel=r.id and lp.tipo_nivel=1 and lp.estado=1)
						LEFT JOIN {$GLOBALS["BD_NAME"]}.lista_precio l ON (l.id=lp.id_lista)
						GROUP BY r.id
						;";
				break;
			case 1:
				$sql = "SELECT 'Regional' as tipo,'1' as tiponum,r.id as tipo_id,r.nombre as nombre_tipo,if(lp.id_lista is null,0,group_concat(lp.id_lista))as id_lista,if(group_concat(concat(if(l.id_distribuidor=0,'(Operador)','(Distribuidor)'),l.nombre))is null,'GENERAL',group_concat(concat(if(l.id_distribuidor=0,'(Operador)','(Distribuidor)'),l.nombre))) as nombre_lista,lp.estado,group_concat(lp.id_distribuidor) as id_distribuidor,if(l.estado_vigencia is null,1,l.estado_vigencia) as estado_vigencia
						FROM {$GLOBALS["BD_NAME"]}.regional r
						LEFT JOIN  {$GLOBALS["BD_NAME"]}.niveles_listaprecios lp ON (lp.id_nivel=r.id and lp.tipo_nivel=$tipo and lp.id_nivel in ($id_tipo) and lp.estado=1)
						LEFT JOIN {$GLOBALS["BD_NAME"]}.lista_precio l ON (l.id=lp.id_lista)
						WHERE r.id in ($id_tipo)
						GROUP BY r.id
						;";
				break;
		}
		$response = $this->DB->devolver_array($sql);
		return $response;
	}

	public function consultar_listas()
	{

		$consulta = "SELECT id, nombre FROM {$GLOBALS["BD_NAME"]}.lista_precio where estado=1 and id_distribuidor = 0;";
		$response = $this->DB->devolver_array($consulta);
		return $response;
	}

	public function consultar_items($id_lista)
	{
		$consulta = "SELECT lp.id,r.producto,r.pn,il.precio_venta as precio_pdv,il.precioventa_directo as precio_publico FROM {$GLOBALS["BD_NAME"]}.items_lista il 
					INNER JOIN {$GLOBALS["BD_NAME"]}.lista_precio lp ON (lp.id=il.id_lista)
					INNER JOIN {$GLOBALS["BD_NAME"]}.referencias r ON (r.id=il.id_referencia) 
					WHERE lp.id=$id_lista;";
		$response = $this->DB->devolver_array($consulta);
		foreach ($response as $key => $value) {
			$response[$key]["producto"] = utf8_encode($response[$key]["producto"]);
		}
		return $response;
	}
	public function guardar_lista_tipo($tipo, $tipo_id, $id_lista, $distri_asignacion)
	{

		$error = 0;
		$oSession = unserialize($_SESSION[$GLOBALS["SESION_OP"]]);
		$id_user = $oSession->VSid;
		$id_l_or = 0;

		//VALIDACION DE QUE NO EXISTA UNA ASIGNACION YA REALIZADA
		$c_asignacion = "SELECT id 
						 FROM {$GLOBALS["BD_NAME"]}.niveles_listaprecios
						 WHERE tipo_nivel = $tipo AND id_nivel = $tipo_id AND id_distribuidor = 0 AND estado = 1;";

		$r_asignacion = $this->DB->devolver_array($c_asignacion);

		if (count($r_asignacion) > 0) {
			return array('evento' => -1, 'msg' => 'La asignacion ya se ha realizado.');
			exit();
		}


		//VALIDACION DE QUE NO EXISTA UNA ASIGNACION YA REALIZADA FIN

		$this->DB->consultar("BEGIN");

		$consulta = "SELECT id,if(estado=1,id_lista,0) as id_lista FROM {$GLOBALS["BD_NAME"]}.niveles_listaprecios WHERE tipo_nivel = $tipo and id_nivel = $tipo_id and estado=0 and id_distribuidor=0;";
		$datos = $this->DB->devolver_array($consulta);
		if (count($datos) > 0) {
			$id_l_or = $datos[0]["id_lista"];
			$sql = "UPDATE niveles_listaprecios SET estado = 1,id_lista=$id_lista WHERE id = " . $datos[0]["id"] . ";";

			$sql_audi = "INSERT INTO audi_niveles_listaprecios(id_lista, tipo_nivel, id_nivel, estado, fecha, hora, usuario, accion)
			VALUES($id_lista,$tipo,$tipo_id,1,curdate(),curtime(),$id_user,2);";

			if ($distri_asignacion == 1) {
				$u_asignacion_distrip = "UPDATE niveles_listaprecios
											SET
											estado = 0
											WHERE tipo_nivel = $tipo AND id_nivel = $tipo_id AND id_distribuidor > 0;";
			}
		} else {

			$sql = "INSERT INTO niveles_listaprecios(tipo_nivel,id_nivel,id_lista,estado)VALUES($tipo,$tipo_id,$id_lista,1);";

			$sql_audi = "INSERT INTO audi_niveles_listaprecios(id_lista, tipo_nivel, id_nivel, estado, fecha, hora, usuario, accion,id_distribuidor)
			VALUES($id_lista,$tipo,$tipo_id,1,curdate(),curtime(),$id_user,1,0);";

			if ($distri_asignacion == 1) {
				$u_asignacion_distrip = "UPDATE niveles_listaprecios
											SET
											estado = 0
											WHERE tipo_nivel = $tipo AND id_nivel = $tipo_id AND id_distribuidor > 0;";
			}
		}

		$c_listap = "SELECT estado_vigencia FROM {$GLOBALS["BD_NAME"]}.lista_precio WHERE id = $id_lista;";
		$r_listap = $this->DB->devolver_array($c_listap);

		if ($r_listap[0]["estado_vigencia"] == 1) { //VALIDACION DE LA VIGENCIA DE LA LISTA DE PRECIOS
			//ACTUALIZACION DEL PADRE DE LA LISTA DE PRECIOS


			$c_distrib_lista = "SELECT id_distri FROM {$GLOBALS["BD_NAME"]}.regionales_distri where id_reg = $tipo_id GROUP BY id_distri;";
			$r_distrib_lista = $datos = $this->DB->devolver_array($c_distrib_lista);
			$distrib = "";
			if (count($r_distrib_lista) > 0) {

				if (count($r_distrib_lista) > 1) {
					foreach ($r_distrib_lista as $value) {
						$c_distri_niv = "SELECT id
																FROM {$GLOBALS["BD_NAME"]}.niveles_listaprecios 
																WHERE id_nivel in (SELECT id_reg FROM {$GLOBALS["BD_NAME"]}.regionales_distri WHERE id_distri={$value["id_distri"]}) AND tipo_nivel = 1 AND estado = 1 and id_distribuidor=0;";
						$r_distri_niv = $this->DB->devolver_array($c_distri_niv);
						if (count($r_distri_niv) > 0) {
							$distrib .= $value["id_distri"] . ",";
						}
					}

					if ($distrib != "") {
						$distrib = substr($distrib, 0, -1);
					}
				} else {
					$c_distri_niv = "SELECT id
																FROM {$GLOBALS["BD_NAME"]}.niveles_listaprecios 
																WHERE id_nivel in (SELECT id_reg FROM {$GLOBALS["BD_NAME"]}.regionales_distri WHERE id_distri={$r_distrib_lista[0]["id_distri"]}) AND tipo_nivel = 1 AND estado = 1 and id_distribuidor=0;";
					$r_distri_niv = $this->DB->devolver_array($c_distri_niv);
					if (count($r_distri_niv) > 0) {
						$distrib = $r_distrib_lista[0]["id_distri"];
					}
				}
				if ($distrib != "") {
					$u_origen_lista = "UPDATE {$GLOBALS["BD_NAME"]}.lista_precio
														SET
														id_lista_origen = $id_lista
														WHERE id_distribuidor in ($distrib) and  id_lista_origen = $id_l_or ;";

					$res = $this->DB->consultar($u_origen_lista);
					if (!$res) {

						$error = 1;
					}
				}
			}
			//ACTUALIZACION DEL PADRE DE LA LISTA DE PRECIOS
		} //VALIDACION DE LA VIGENCIA DE LA LISTA DE PRECIOS FIN
		if ($r_listap[0]["estado_vigencia"] == 1) { //VALIDACION DE LA VIGENCIA DE LA LISTA DE PRECIOS
			if ($distri_asignacion == 1) {
				$res = $this->DB->consultar($u_asignacion_distrip);
				if (!$res) {

					$error = 1;
				}
			}
		} //VALIDACION DE LA VIGENCIA DE LA LISTA DE PRECIOS FIN

		$res = $this->DB->consultar($sql);
		if (!$res) {

			$error = 1;
		}

		$res = $this->DB->consultar($sql_audi);
		if (!$res) {

			$error = 1;
		}

		$error = $this->DB->actualizar_listas_cascada($id_lista);


		if ($error != 1) {
			$this->DB->consultar("COMMIT");
			$this->envio_notificaciones($id_lista, $id_lista);
			return array('evento' => 1, 'msg' => 'Se ha asignado la lista de precios con exito');
		} else {
			$this->DB->consultar("ROLLBACK");
			return array('evento' => -1, 'msg' => 'Error al intentar asignar la lista de precios');
		}
	}

	public function deshabilitar_asignacion_lista($tipo, $tipo_id, $id_lista)
	{
		$error = 0;
		$id_l_or = 0;

		$oSession = unserialize($_SESSION[$GLOBALS["SESION_OP"]]);
		$id_user = $oSession->VSid;
		$consulta = "SELECT nl.id,nl.id_lista 
					FROM {$GLOBALS["BD_NAME"]}.niveles_listaprecios nl 
					INNER JOIN {$GLOBALS["BD_NAME"]}.lista_precio lp ON (nl.id_lista=lp.id)
					WHERE nl.tipo_nivel=$tipo and nl.id_nivel=$tipo_id and nl.id_lista in($id_lista) and nl.estado=1 and lp.id_distribuidor=0;";
		$datos = $this->DB->devolver_array($consulta);

		$this->DB->consultar("BEGIN");

		if (count($datos) > 0) {

			$id_l_or = $datos[0]["id_lista"];
			$sql = "UPDATE {$GLOBALS["BD_NAME"]}.niveles_listaprecios SET estado = 0 WHERE id = " . $datos[0]["id"] . ";";

			$sql_audi = "INSERT INTO {$GLOBALS["BD_NAME"]}.audi_niveles_listaprecios(id_lista, tipo_nivel, id_nivel, estado, fecha, hora, usuario, accion)
			VALUES($id_lista,$tipo,$tipo_id,0,curdate(),curtime(),$id_user,2);";

			//ACTUALIZACION DEL PADRE DE LA LISTA DE PRECIOS
			$c_distrib_lista = "SELECT id_distri FROM {$GLOBALS["BD_NAME"]}.regionales_distri where id_reg = $tipo_id GROUP BY id_distri;";
			$r_distrib_lista = $datos = $this->DB->devolver_array($c_distrib_lista);
			$distrib = "";
			if (count($r_distrib_lista) > 0) {

				if (count($r_distrib_lista) > 1) {
					foreach ($r_distrib_lista as $value) {
						$c_distri_niv = "SELECT id 
																FROM {$GLOBALS["BD_NAME"]}.niveles_listaprecios 
																WHERE id_nivel in (SELECT id_reg FROM {$GLOBALS["BD_NAME"]}.regionales_distri WHERE id_distri={$value["id_distri"]}) AND tipo_nivel = 1 AND estado = 1;";
						$r_distri_niv = $this->DB->devolver_array($c_distri_niv);
						if (count($r_distri_niv) > 0) {
							$distrib .= $value["id_distri"] . ",";
						}
					}

					if ($distrib != "") {
						$distrib = substr($distrib, 0, -1);
					}
				} else {
					$c_distri_niv = "SELECT id 
																FROM {$GLOBALS["BD_NAME"]}.niveles_listaprecios 
																WHERE id_nivel in (SELECT id_reg FROM {$GLOBALS["BD_NAME"]}.regionales_distri WHERE id_distri={$r_distrib_lista[0]["id_distri"]}) AND tipo_nivel = 1 AND estado = 1;";
					$r_distri_niv = $this->DB->devolver_array($c_distri_niv);

					if (count($r_distri_niv) > 0) {
						$distrib = $r_distrib_lista[0]["id_distri"];
					}
				}
				if ($distrib != "") {
					$u_origen_lista = "UPDATE {$GLOBALS["BD_NAME"]}.lista_precio
														SET
														id_lista_origen = 0
														WHERE id_distribuidor in ($distrib) and  id_lista_origen = $id_l_or ;";

					$res = $this->DB->consultar($u_origen_lista);
					if (!$res) {

						$error = 1;
					}
				}
			}
			//ACTUALIZACION DEL PADRE DE LA LISTA DE PRECIOS

			$res = $this->DB->consultar($sql);

			if (!$res) {
				$error = 1;
			}

			$res = $this->DB->consultar($sql_audi);

			if (!$res) {
				$error = 1;
			}

			$error = $this->DB->actualizar_listas_cascada(0);
		}

		if ($error != 1) {
			$this->DB->consultar("COMMIT");
			$this->envio_notificaciones(0, $id_lista);
			return array('evento' => 1, 'msg' => 'Se ha quitado la lista de precios con exito');
		} else {
			$this->DB->consultar("ROLLBACK");
			return array('evento' => -1, 'msg' => 'Error al intentar quitar la lista de precios');
		}
	}

	function traerCanales()
	{
		return array();
	}

	function traerDistribuidores()
	{
		return array();
	}

	function envio_notificaciones($id_lista_origen, $id_lista)
	{/*
		$usuario_con = "";
		$condicion = "";
		//CONSULTA DE SI ESTA EN CASCADA O NORMAL
    	 $c_config = "SELECT actualizacion_cascada, precio_menor FROM {$GLOBALS["BD_NAME"]}.configuracion_stock;";
		 $r_config = $this->DB->devolver_array($c_config);
		//CONSULTA DE SI ESTA EN CASCADA O NORMAL FIN
		$tipo_noti = "103";
		$id_list_c = "";
		if(intval($r_config[0]["actualizacion_cascada"])==1){
			$id_list_c = $id_lista_origen;
		}else{
			$id_list_c = $id_lista;
		}

		$c_niveles_list = "SELECT nl.tipo_nivel,nl.id_nivel 
						   FROM lista_precio l
						   INNER JOIN niveles_listaprecios nl ON (l.id = nl.id_lista)
						   WHERE l.id_lista_origen in ($id_list_c) OR l.id in ($id_lista);";
		$r_niveles_list = $this->DB->devolver_array($c_niveles_list);

		foreach ($r_niveles_list as $niveles) {
			switch (intval($niveles["tipo_nivel"])) {
				case 1://REGIONAL
					$c_distri_reg = "SELECT CONCAT('{$GLOBALS["BD_DIS"]}',nombre_corto,'_',id)AS bd_distri 
	    						 FROM distribuidores WHERE id IN (SELECT id_distri FROM regionales_distri WHERE id_reg = {$niveles["id_nivel"]})";

		    		$r_distri_reg = $this->DB->devolver_array($c_distri_reg);

		    		foreach ($r_distri_reg as $bd_distri) {
		    			$c_permisos_usu = " SELECT id,token_notif FROM {$bd_distri["bd_distri"]}.usuarios 
		    								WHERE token_notif IS NOT NULL 
		    								AND id IN  (SELECT id_usus FROM {$bd_distri["bd_distri"]}.niveles_dcs WHERE (id_tipo = {$niveles["id_nivel"]} and tipo = 1) OR p4 = {$niveles["id_nivel"]} group by id_usus)";

		    			$r_permisos_usu = $this->DB->devolver_array($c_permisos_usu);
		    			foreach ($r_permisos_usu as $usuarios) {
		    				$usuario_con .= $usuarios["id"].",";
		    				 $this->DB->notificar($usuarios["token_notif"],"","",$tipo_noti);
		    			}
		    		}
				break;
				case 2://TERRITORIO
					if($usuario_con != ""){
						$usuario_con = substr($usuario_con,0,-1);
						$condicion = " AND id NOT IN ($usuario_con) ";
					}
					$c_distri_reg = "SELECT CONCAT('{$GLOBALS["BD_DIS"]}',nombre_corto,'_',id)AS bd_distri 
	    						 FROM distribuidores WHERE id IN (SELECT id_distri FROM territorios_distribuidor WHERE id_territorio = {$niveles["id_nivel"]} GROUP BY id_distri)";

		    		$r_distri_reg = $this->DB->devolver_array($c_distri_reg);

		    		foreach ($r_distri_reg as $bd_distri) {
		    			$c_permisos_usu = " SELECT id,token_notif FROM {$bd_distri["bd_distri"]}.usuarios 
		    								WHERE token_notif IS NOT NULL 
		    								AND id IN (SELECT id_usus FROM {$bd_distri["bd_distri"]}.niveles_dcs WHERE (id_tipo = {$niveles["id_nivel"]} and tipo = 3) OR p2 = {$niveles["id_nivel"]} group by id_usus) $condicion ";

		    			$r_permisos_usu = $this->DB->devolver_array($c_permisos_usu);

		    			foreach ($r_permisos_usu as $usuarios) {
		    				$usuario_con .= $usuarios["id"].",";
		    				$this->DB->notificar($usuarios["token_notif"],"","",$tipo_noti);
		    			}
		    		}

					
				break;
				case 3://ZONA
					if($usuario_con != ""){
						$usuario_con = substr($usuario_con,0,-1);
						$condicion = " AND id NOT IN ($usuario_con) ";
					}
					$c_distri_reg = "SELECT CONCAT('{$GLOBALS["BD_DIS"]}',nombre_corto,'_',id)AS bd_distri 
	    						 FROM distribuidores WHERE id IN (SELECT id_distri FROM territorios_distribuidor WHERE id_territorio IN (SELECT territorio FROM zonas WHERE id = $id_tipo) GROUP BY id_distri)";
		    		$r_distri_reg = $this->DB->devolver_array($c_distri_reg);

		    		foreach ($r_distri_reg as $bd_distri) {
		    			$c_permisos_usu = " SELECT id,token_notif FROM {$bd_distri["bd_distri"]}.usuarios 
		    								WHERE token_notif IS NOT NULL 
		    								AND id IN  (SELECT id_usus FROM {$bd_distri["bd_distri"]}.niveles_dcs WHERE (id_tipo = {$niveles["id_nivel"]} and tipo = 4) OR p1 = {$niveles["id_nivel"]} group by id_usus) $condicion ";

		    			$r_permisos_usu = $this->DB->devolver_array($c_permisos_usu);

		    			foreach ($r_permisos_usu as $usuarios) {
		    				$usuario_con .= $usuarios["id"].",";
		    				$this->DB->notificar($usuarios["token_notif"],"","",$tipo_noti);
		    			}
		    		}
				break;
			}
		}	   			
	    	
	*/
	}
}


// Fin clase

const URL_MODULO = 'modulos/asignar_menu_perfiles';
var table_comple = "";
var res = "";
var dtable;

$(document).ready(function () {

	ver_nom_perfil();

	$("#bt_usu_search").click(function () {
		show(0);
	});

});


//Cargar Tabla con los menús, si estan asignados, estarán marcados por defecto.
function show(id_perfil) {
	var id;
	var checked;

	var nom_pro = $("#nom_pro").val();
	//$("#perfiles").html("<img src = 'template/images/loading.gif'>");
	$.post('modulos/asignar_menu_perfiles/controlador.php',
		{
			accion: 'show',
			id_perfil: id_perfil,
			nom_pro: nom_pro
		},
		function (data, textStatus) {
			localStorage.setItem("men", data);
			var datos = JSON.parse(data);
			var menu = datos["menu"];
			var perfiles = datos["perfiles"];

			var tabla3 = '<table id="list_menu" border="0"  class="table table-striped table-bordered nowrap" cellspacing="0" width="100%">';
			tabla3 += '<thead><tr>';
			tabla3 += '<th class="no-sort"><label>Menú</label></th>';
			tabla3 += '<th colspan="' + perfiles.length + '"><label>Perfiles</label></th>';
			tabla3 += '</tr>';
			tabla3 += '<tr>';
			tabla3 += '<th>Nombre</th>';

			if (perfiles.length > 0) {
				$.each(perfiles, function (index, fila) {
					tabla3 += '<th id="perfil_' + fila.id + '">' + fila.nombre + ' <input type="hidden" name="perfiles[]" value = "' + fila.id + '"; /><input type="checkbox"  chek-all-per="' + fila.id + '" onclick="check_all_menus(' + fila.id + ');" name="check_all"  id="check_all_' + fila.id + '" title="Asignar todos los Menús a `' + fila.nombre + '´ "></th>';
				});
			} else {
				tabla3 += "<th></th>";
			}

			tabla3 += '</tr></thead>'

			$.each(menu, function (index, fila_menu) {

				tabla3 += "<tr>";

				id_menu = fila_menu.id;
				nom_menu = fila_menu.nombre;
				tabla3 += '<td id="asignar2_' + fila_menu.id + '_' + id_perfil + '"><span style="margin-left:20px;">' + fila_menu.cad + '' + fila_menu.nombre + '</span><input type="checkbox" chek-all="' + fila_menu.id + '" style="float:left;" onclick="check_all_profiles(' + fila_menu.id + ');" name="check_all_pr"  id="check_all_pr' + fila_menu.id + '" title="Asignar `' + fila_menu.nombre + '´ a todos los Perfiles"></td>';

				if (perfiles.length > 0) {
					$.each(perfiles, function (index, fila) {
						checks = "";
						$.each(fila.menu_perfil, function (index, fila_men) {
							if (fila_men.id_perfil == fila.id && fila_men.id_menu == id_menu) {
								checks = "checked";
							}
						});

						tabla3 += '<td align="center"><input type="checkbox" name="asignar_check[]" value="' + id_menu + '|' + fila.id + '" id="asignar_check_' + id_menu + '_' + fila.id + '" ' + checks + ' title="Añadir `' + nom_menu + '´ al perfil `' + fila.nombre + '´"></td>';
					});
				} else {
					tabla3 += "<td></td>";
				}

				tabla3 += "</tr>";

			});

			tabla3 += "</table>";
			$("#perfiles").html(tabla3);
			html_buttons = '<button type="button" name="cl_bt" id="bt_usu_search2" class="btn">Cancelar</button>';
			html_buttons += ' <button type="submit" name="button" id="enviar_accesos" class="btn btn-primary">Guardar</button>';
			$("#div_buttons").html(html_buttons)

			$("#bt_usu_search2").click(function () {
				show(0);
			});

			$('#enviar_accesos').click(function () {
				proof();
			});

			check_hijos();
			$("#btn_asig").empty();
			//Menus all
			$.each(menu, function (index, fila) {
				sum = 0
				for (i = 0; i < perfiles.length; i++) {
					if ($("#asignar_check_" + fila.id + "_" + perfiles[i].id).is(':checked')) {
						sum++
					}
				}
				if (sum == perfiles.length) {
					$("input[chek-all=" + fila.id + "]").prop("checked", true).attr("chek-all", fila.id);
				}
				else if (sum > 0) {
					$("input[chek-all=" + fila.id + "]").prop("indeterminate", true).attr("chek-all", fila.id);
				}
			});
			//Perfiles all
			$.each(perfiles, function (index, fila) {
				sum = 0
				for (i = 0; i < menu.length; i++) {
					if ($("#asignar_check_" + menu[i].id + "_" + fila.id).is(':checked')) {
						sum++
					}
				}
				if (sum == menu.length) {
					$("input[chek-all-per=" + fila.id + "]").prop("checked", true).attr("chek-all", fila.id);
				}
				else if (sum > 0) {
					$("input[chek-all-per=" + fila.id + "]").prop("indeterminate", true).attr("chek-all", fila.id);
				}
			});

			$("input[type=checkbox]").click(function () {
				var num_per = perfiles.length;
				var fila = $(this).val().split("|");
				var seleccionadas = 0;
				//Menus all
				$.each(perfiles, function (index, fila_per) {
					if ($("#asignar_check_" + fila[0] + "_" + fila_per.id).is(':checked')) {
						seleccionadas++;
					}
				});

				if (seleccionadas == num_per) {
					$("input[chek-all=" + fila[0] + "]").prop("checked", true);
					$("input[chek-all=" + fila[0] + "]").prop("indeterminate", false);
				}
				else if (seleccionadas > 0) {
					$("input[chek-all=" + fila[0] + "]").prop("indeterminate", true);
				}
				else {
					$("input[chek-all=" + fila[0] + "]").prop("checked", false);
					$("input[chek-all=" + fila[0] + "]").prop("indeterminate", false);
				}

				//Perfiles all
				var num_select = 0;
				var num_menu = menu.length
				$.each(menu, function (index, fila_per) {
					if ($("#asignar_check_" + fila_per.id + "_" + fila[1]).is(':checked')) {
						num_select++;
					}
				});

				if (num_select == num_menu) {
					$("input[chek-all-per=" + fila[1] + "]").prop("checked", true);
					$("input[chek-all-per=" + fila[1] + "]").prop("indeterminate", false);
				}
				else if (num_select > 0) {
					$("input[chek-all-per=" + fila[1] + "]").prop("indeterminate", true);
				}
				else {
					$("input[chek-all-per=" + fila[1] + "]").prop("checked", false);
					$("input[chek-all-per=" + fila[1] + "]").prop("indeterminate", false);
				}


			});


			if (!$.fn.DataTable.isDataTable('#list_menu')) {
				dtable = $('#list_menu').DataTable({
					scrollY: "500px",
					scrollX: true,
					scrollCollapse: true,
					paging: false,
					fixedColumns: true,
					"bFilter": false,
					"order": []
				});
			}
			else {
				dtable.destroy();
				dtable = $('#list_menu').DataTable({
					scrollY: "500px",
					scrollX: true,
					scrollCollapse: true,
					paging: false,
					fixedColumns: true,
					"bFilter": false,
					"order": []
				});
			}

		}
	);

}

function check_all_menus(id_perfil) {
	var check_all = document.getElementById("check_all_" + id_perfil);
	$('input[name="asignar_check[]"]').each(function (index, element) {
		var id_men = $(this).val().split("|");
		if (check_all.checked) {
			$("#asignar_check_" + id_men[0] + "_" + id_perfil).prop("checked", true);
		} else {
			$("#asignar_check_" + id_men[0] + "_" + id_perfil).prop("checked", false);
		}
	});
}

function check_all_profiles(id_menu) {
	var check_all = $("input[chek-all=" + id_menu + "]");
	$('input[name="asignar_check[]"]').each(function (index, element) {
		var id_men = $(this).val().split("|");
		$(check_all).each(function (index, element) {
			if ($(this).is(":checked")) {
				$("#asignar_check_" + id_menu + "_" + id_men[1]).prop("checked", true);
			} else {
				$("#asignar_check_" + id_menu + "_" + id_men[1]).prop("checked", false);
			}
		});
	});

}


//Cargar nombres de los perfiles en el datalist
function ver_nom_perfil() {

	$.post('modulos/asignar_menu_perfiles/controlador.php',
		{
			accion: 'ver_nom_perfil'
		},
		function (data, textStatus) {

			data = JSON.parse(data);
			var datalist = "<option  value=''>Seleccione el nombre del perfil...</option>";
			$.each(data, function (index, fila) {
				datalist += "<option value='" + fila.idper + "'>" + fila.nom_perfil + "</option>";

			});

			$("#nom_pro").html(datalist);

		}
	);

}

//Obtener menus a los que el perfil tiene acceso
function check_hijos() {
	//var checkeados= new Array();

	$('input[name="asignar[]"]:checked').each(function () {
		//$(this).val() es el valor del checkbox correspondiente	
		var res = $(this).val().split(",");
		check(res[1], res[3], 1);
	});
}

function check_all(id_perfil) {

	var check_all = document.getElementById("check_all_" + id_perfil);

	$('input[name="asignar[]"]').each(function (index, element) {

		var id_menu = $(this).val().split(",");

		if (check_all.checked) {

			document.getElementById("asignar_" + id_menu[1] + "_" + id_perfil).checked = true;
			$("#asignar_" + id_menu[1] + "_" + id_perfil).attr("onclick", 'check(' + id_menu[1] + ',' + id_perfil + ',1)');
			$("#asignar_" + id_menu[1] + "_" + id_perfil).attr("disabled", false);

		} else {

			$("#asignar_" + id_menu[1] + "_" + id_perfil).attr("checked", false);

			if (id_menu[2] != 0) {
				$("#asignar_" + id_menu[1] + "_" + id_perfil).removeAttr("onclick");
				$("#asignar_" + id_menu[1] + "_" + id_perfil).attr("disabled", true);
			}

		}

	});
}


//Habilitar hijos cuando se selecciona un menú padre
function check(id, id_perfil, op) {

	var asig = document.getElementById("asignar_" + id + "_" + id_perfil);
	var data = JSON.parse(localStorage.getItem('men'));
	var menu = data["menu"];

	if (op == 1) {

		if (!asig.checked) {

			$("#asignar_" + id + "_" + id_perfil).attr("checked", false);

			check2(id, id_perfil, 1);

			$.each(menu, function (index, fila) {

				if (fila.checked == 1) {
					$("#check_all_" + id_perfil).attr("checked", true);
				}

				//check2(fila.id,id_perfil,1);

			});

		}
		else {

			$("#asignar_" + id + "_" + id_perfil).attr("checked", true);

			$.each(menu, function (index, fila) {
				//$("#td_"+id).html('<input type="checkbox" name="asignar[]" value="1,'+id+'" onclick="check('+id+');" checked id="asignar_'+id+'">');

				if (fila.checked == 1) {
					$("#check_all_" + id_perfil).attr("checked", true);
				}

				if (id == fila.idpadre) {
					document.getElementById("asignar_" + fila.id + "_" + id_perfil).disabled = false;
					$("#asignar_" + fila.id + "_" + id_perfil).attr("onclick", 'check(' + fila.id + ',' + id_perfil + ',1);');
					$("#asignar2_" + fila.id + "_" + id_perfil).attr("onclick", 'check(' + fila.id + ',' + id_perfil + ',2);');
					$("#asignar3_" + fila.id + "_" + id_perfil).attr("onclick", 'check(' + fila.id + ',' + id_perfil + ',2);');
				}
			});

		}
	} else {


		if (asig.checked == false) {

			//$("#asignar_"+id+"_"+id_perfil).attr("checked",'true');
			document.getElementById("asignar_" + id + "_" + id_perfil).checked = true;

			$.each(menu, function (index, fila) {

				if (fila.checked == 1) {
					document.getElementById("check_all_" + id_perfil).checked = true;
				}

				if (id == fila.idpadre) {
					document.getElementById("asignar_" + fila.id + "_" + id_perfil).disabled = false;
					$("#asignar_" + fila.id + "_" + id_perfil).attr("onclick", 'check(' + fila.id + ',' + id_perfil + ',1);');
					$("#asignar2_" + fila.id + "_" + id_perfil).attr("onclick", 'check(' + fila.id + ',' + id_perfil + ',2);');
					$("#asignar3_" + fila.id + "_" + id_perfil).attr("onclick", 'check(' + fila.id + ',' + id_perfil + ',2);');
				}
			});


		}
		else {

			//$("#asignar_"+id+"_"+id_perfil).attr("checked",'false');
			document.getElementById("asignar_" + id + "_" + id_perfil).checked = false;
			check2(id, id_perfil, 2);
			$.each(menu, function (index, fila) {

				if (fila.checked == 1) {
					document.getElementById("check_all_" + id_perfil).checked = true;
				}

			});

		}

	}

}

function check2(id, id_perfil) {

	var asig = document.getElementById("asignar_" + id + "_" + id_perfil);
	var data = JSON.parse(localStorage.getItem('men'));
	var menu = data["menu"];

	if (!asig.checked) {

		$.each(menu, function (index, fila) {

			if (id == fila.idpadre) {

				$("#asignar_" + fila.id + "_" + id_perfil).attr("disabled", 'true');
				document.getElementById("asignar_" + fila.id + "_" + id_perfil).checked = false;
				$("#asignar_" + fila.id + "_" + id_perfil).removeAttr("onclick");

				check2(fila.id, id_perfil)

			}

		});

	}
}


var cont_env = 0;


function proof() {
	var checkboxValues = new Array();
	var id_perfiles = new Array();
	var estadoCheck = new Array();

	$('input[name="perfiles[]"]').each(function () {
		id_perfiles.push($(this).val());
	});

	$('input[name="asignar_check[]"]').each(function () {
		//$(this).val() es el valor del checkbox correspondiente
		var res = $(this).val().split("|");
		var id_menu = res[0];
		var id_perfil = res[1];
		var estados = 0;
		if (document.getElementById("asignar_check_" + id_menu + "_" + id_perfil).checked) {
			var estados = 1;
			estadoCheck.push('estados[]=' + estados);
		}
		checkboxValues.push(estados + "," + id_menu + "," + id_perfil);
	});


	if (estadoCheck.join('&') == "") {
		Notificacion("Selecciona un acceso para el perfil", "warning");
	} else {

		if (cont_env == 0) {

			BootstrapDialog.confirm('¿Desea guardar los accesos para los perfiles?', function (result) {
				if (result) {
					$("#respuesta").html("<center><div style='top:300px;'>Espera un momento... <img src = 'template/images/loading.gif'></div></center>");
					$("#respuesta").fadeIn("fast");
					$("#respuesta").css("z-index", "1910");
					$("<div>", {
						class: 'overlay',
						id: 'overlay'
					}).appendTo('body').fadeIn("fast");

					$.post(
						'modulos/asignar_menu_perfiles/controlador.php',
						{
							accion: 'Crea_Perfil_Accesos',
							id_perfiles: id_perfiles,
							checkboxValues: checkboxValues
						},
						function (data, textStatus) {

							if (confirma_rest(data, 'Crea_Perfil_Accesos')) {
								if (data != 0 || data != "") {
									if (data == "S") {

										$("#overlay").remove();
										$("#respuesta").fadeOut("fast");
										Notificacion("Los cambios se realizaron satisfactoriamente!", "success");
										cont_env = 0;
										show(0);
									}
									else if (data == "N") {

										$("#overlay").remove();
										$("#respuesta").fadeOut("fast");
										Notificacion("No se han podido realizar los cambios!", "error");
										cont_env = 0;
									}
								}
							}
						}
					);
				} else {
					cont_env = 0;
					show(0);
				}

			});

			cont_env++;
		}
	}
}



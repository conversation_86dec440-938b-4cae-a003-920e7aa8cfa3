<?php
require("../../config/mainModel.php");


class admin_menu_perfiles
{

	//Espacio Para declara las funciones que retornan los datos de la DB.
	public $array_response;
	public $cont = 0;
	public $con_niv = array();
	public $BD;

	public function __construct()
	{
		$this->BD = new BD();
		$this->BD->conectar();
	}

	public function __destruct()
	{
		$this->BD->desconectar();
	}

	//Listar nombres de los perfiles
	public function Retornar_nom_perfil()
	{
		$resp = array();
		$array_response = array();

		$sql = "SELECT id,descripcion FROM perfiles WHERE estado = 1 ORDER BY id";
		$res = $this->BD->consultar($sql);
		while (!$res->EOF) {

			$array_response[] = array("nom_perfil" => ucwords($res->fields["descripcion"]), "idper" => $res->fields["id"]);

			$res->MoveNext();
		}

		return $array_response;
	}

	//Lista perfil por nombre
	public function Get_Profile($idper)
	{
		$resp = array();
		$array_response = array();
		$array_men_per = array();

		$sql1 = "SELECT * FROM perfiles WHERE id = " . $idper . " AND estado = 1 ";
		$res = $this->BD->consultar($sql1);
		while (!$res->EOF) {

			$queryU = "SELECT CONCAT(nombre,' ',apellido) AS usuario_crea FROM usuarios WHERE id = " . $res->fields["usu_crea"];
			$resU = $this->BD->consultar($queryU);


			$sql = "SELECT id_perfil,id_menu FROM menu_perfiles WHERE id_perfil = " . $res->fields["id"] . " and estado = 1";
			$resMen = $this->BD->consultar($sql);

			while (!$resMen->EOF) {

				$array_men_per[] = array("id_perfil" => $resMen->fields["id_perfil"], "id_menu" => $resMen->fields["id_menu"]);

				$resMen->MoveNext();
			}

			$array_response[] = array("id" => $res->fields["id"], "nombre" => ucwords($res->fields["descripcion"]), "tipo_acceso" => 0, "fecha_crea" => $res->fields["fecha_crea"], "usuario_crea" => $resU->fields["usuario_crea"], "estado" => $res->fields["estado"], "sms" => '', "email" => '', "menu_perfil" => $array_men_per);


			$res->MoveNext();
		}

		//$array_response = array('perfiles' => $resp);

		return $array_response;
	}

	//Lista de los perfiles
	public function Retornar_Perfiles()
	{
		//Si $id esta vacio devuelve todos los perfiles.
		$resp = array();
		$array_response = array();
		$array_men_per = array();

		global $nombre_perfil, $estado_perfil;

		$sql = "SELECT * FROM perfiles WHERE id != 0 AND estado = 1";

		$sql .= " ORDER BY id";

		$res = $this->BD->consultar($sql);

		while (!$res->EOF) {

			$queryU = "SELECT CONCAT(nombre,' ',apellido) AS usuario_crea FROM usuarios WHERE id = " . $res->fields["usu_crea"];
			$resU = $this->BD->consultar($queryU);


			$sql = "SELECT id_perfil,id_menu FROM menu_perfiles WHERE id_perfil = " . $res->fields["id"] . " and estado = 1";
			$resMen = $this->BD->consultar($sql);

			while (!$resMen->EOF) {

				$array_men_per[] = array("id_perfil" => $resMen->fields["id_perfil"], "id_menu" => $resMen->fields["id_menu"]);

				$resMen->MoveNext();
			}

			$array_response[] = array("id" => $res->fields["id"], "nombre" => ucwords($res->fields["descripcion"]), "tipo_acceso" => 0, "fecha_crea" => $res->fields["fecha_crea"], "usuario_crea" => $resU->fields["usuario_crea"], "estado" => $res->fields["estado"], "sms" => '', "email" => '', "menu_perfil" => $array_men_per);


			$res->MoveNext();
		}

		//$array_response = array('perfiles' => $resp);

		return $array_response;
	}


	public function Crear_Perfil_Accesos($id_perfiles, $checkboxValues)
	{
		if (isset($_SESSION[$GLOBALS["SESION_OP"]])) {

			$oSession = unserialize($_SESSION[$GLOBALS["SESION_OP"]]);
			$idu = $oSession->VSid;

			$res = "";
			$fecha = date("Y/m/d");
			$hora = date("H:i:s");
			$id = "";
			$cad = array();
			$error = 0;

			if ($checkboxValues != "") {

				$this->BD->consultar("BEGIN");

				foreach ($id_perfiles as $values_per) {

					$id_perfil = $values_per;

					foreach ($checkboxValues as $values) {

						$cad = explode(",", $values);

						if ($id_perfil == intval($cad[2])) {

							$sql = "SELECT id_perfil FROM menu_perfiles WHERE id_perfil = $id_perfil and id_menu = " . intval($cad[1]);
							$res_con = $this->BD->consultar($sql);

							$estado = intval($cad[0]);

							if ($this->BD->numreg($res_con) > 0) {

								$squ = "UPDATE menu_perfiles SET estado = " . $estado . "  WHERE id_perfil = $id_perfil and id_menu = " . intval($cad[1]);
								$resu = $this->BD->consultar($squ);
								$sqau = "INSERT INTO audi_menu_perfiles (id_perfil, id_menu, estado, usuario, fecha, hora) VALUES ($id_perfil,'" . intval($cad[1]) . "',$estado,$idu,'$fecha', '$hora')";
								$resau = $this->BD->consultar($sqau);

								if (!$resu) {
									$error = 1;
								}
							} else {

								if ($estado == 1) {

									$squ = "INSERT INTO menu_perfiles (id_perfil, id_menu, estado) VALUES ($id_perfil,'" . intval($cad[1]) . "','$estado') ";
									$resu = $this->BD->consultar($squ);
									$sqaui = "INSERT INTO audi_menu_perfiles (id_perfil, id_menu, estado, usuario, fecha, hora) VALUES ($id_perfil,'" . intval($cad[1]) . "',$estado,$idu,'$fecha', '$hora')";
									$resau = $this->BD->consultar($sqaui);

									if (!$resu) {
										$error = 1;
									}
								}
							}
						}
					}
				}
			}
		} else {
			$error = 1;
		}
		if ($error == 1) {
			$this->BD->consultar("ROLLBACK");
			return "N";
		} else {
			$this->BD->consultar("COMMIT");
			return "S";
		}
	}

	//Crea un nuevo perfil en la base de datos
	public function Crear_Nuevo_Perfil($nom_perfil)
	{

		if (isset($_SESSION[$GLOBALS["SESION_OP"]])) {

			$oSession = unserialize($_SESSION[$GLOBALS["SESION_OP"]]);
			$id = $oSession->VSid;

			$res = "";
			$error = 0;
			$fecha = date("Y/m/d");
			$hora = date("H:i:s");

			$sql = "SELECT id FROM perfiles WHERE descripcion LIKE '" . $nom_perfil . "' ";
			$resi = $this->BD->consultar($sql);

			if ($this->BD->numreg($resi) > 0) {
				$res = "R";
			} else {
				$Query = "INSERT INTO perfiles(descripcion,estado,fecha_crea,hora_crea,usu_crea,sms,email) VALUES('$nom_perfil',1,'$fecha','$hora',$id,0,0)";

				if ($this->BD->consultar($Query)) {
					$id = $this->BD->last_insert_id();
					$sql2 = "INSERT INTO audi_perfiles(id_tipo,descripcion,estado,usuario,movimiento,fecha_mov,hora_mov) VALUES($id,'$nom_perfil',1,$id,1,'$fecha','$hora')";
					$result2 = $this->BD->consultar($sql2);
					$res = "S";
				} else {
					$res = "N";
				}
			}
		} else {
			$res = "N";
		}

		return $res;
	}

	//Edita los campos del perfil identificado bajo el valor de 'id_perfil'
	public function Editar_Perfil($id_perfil, $nombre_perfil)
	{

		if (isset($_SESSION[$GLOBALS["SESION_OP"]])) {

			$oSession = unserialize($_SESSION[$GLOBALS["SESION_OP"]]);
			$id = $oSession->VSid;

			$res = "";
			$fecha = date("Y/m/d");
			$hora = date("H:i:s");

			$sql = "SELECT id FROM perfiles WHERE descripcion LIKE '" . $nombre_perfil . "' AND id != '" . $id_perfil . "' ";
			$res = $this->BD->consultar($sql);

			if ($this->BD->numreg($res) > 0) {
				$res = "R";
			} else {
				$Query = "UPDATE perfiles SET descripcion = '$nombre_perfil'
					  WHERE id = '$id_perfil'";
				if ($this->BD->consultar($Query)) {
					$res = "S";
					$sql2 = "INSERT INTO audi_perfiles(id_tipo,descripcion,estado,usuario,movimiento,fecha_mov,hora_mov) VALUES($id_perfil,'$nombre_perfil',1,$id,2,'$fecha','$hora')";
					$result2 = $this->BD->consultar($sql2);
				} else {
					$res = "N";
				}
			}
		} else {
			return "N";
		}

		return $res;
	}

	//Cambiar el estado de un perfil 
	public function Cambiar_Estado($id_perfil, $estado_perfil)
	{

		if (isset($_SESSION[$GLOBALS["SESION_OP"]])) {

			$oSession = unserialize($_SESSION[$GLOBALS["SESION_OP"]]);
			$id = $oSession->VSid;

			$res = "";
			$new = "";
			$fecha = date("Y/m/d");
			$hora = date("H:i:s");

			if ($estado_perfil == 0) {
				$new = 1;
			} else {
				$new = 0;
			}
			$sql = "SELECT descripcion FROM perfiles WHERE id = $id_perfil";
			$res = $this->BD->consultar($sql);
			$nombre_perfil = $res->fields["descripcion"];

			$Query = "UPDATE perfiles SET estado = '$new'
					  WHERE id = '$id_perfil'";

			if ($this->BD->consultar($Query)) {
				$res = "S";
				$sql2 = "INSERT INTO audi_perfiles(id_tipo,descripcion,estado,usuario,movimiento,fecha_mov,hora_mov) VALUES($id_perfil,'$nombre_perfil',$new,$id,2,'$fecha','$hora')";
				$result2 = $this->BD->consultar($sql2);
			} else {
				$res = "N";
			}
		} else {
			$res = "N";
		}

		return $res;
	}

	//Valida y cambia el valor del campo 'sms'
	public function Validar_SMS($id_perfil, $val_sms)
	{

		if (isset($_SESSION[$GLOBALS["SESION_OP"]])) {

			$oSession = unserialize($_SESSION[$GLOBALS["SESION_OP"]]);
			$idu = $oSession->VSid;

			$res = "";
			$new = "";
			if ($val_sms == 0) {
				$new = 1;
			} else {
				$new = 0;
			}

			$query = "UPDATE perfiles SET sms = '$new' WHERE id = '$id_perfil' ";
			if ($this->BD->consultar($query)) {
				$res = "S";
			} else {
				$res = "N";
			}
		} else {
			$res = "N";
		}
		return $res;
	}

	//Valida y cambia el valor del campo 'email'
	public function Validar_Email($id_perfil, $val_email)
	{

		if (isset($_SESSION[$GLOBALS["SESION_OP"]])) {

			$oSession = unserialize($_SESSION[$GLOBALS["SESION_OP"]]);
			$idu = $oSession->VSid;

			$res = "";
			$new = "";
			if ($val_email == 0) {
				$new = 1;
			} else {
				$new = 0;
			}

			$query = "UPDATE perfiles SET email = '$new' WHERE id = '$id_perfil' ";
			if ($this->BD->consultar($query)) {
				$res = "S";
			} else {
				$res = "N";
			}
		} else {
			$res = "N";
		}

		return $res;
	}


	//Cargar menu
	public function Retornar_Menu()
	{
		$prin = array();
		$array_response = array();

		$sql = "SELECT * FROM menu WHERE estado  = 1";
		$res = $this->BD->consultar($sql);
		$cont = 0;
		$hijos_res = array();

		while (!$res->EOF) {
			$idp = $res->fields["id"];

			$array_response[] = array("cad" => "", "id" => $res->fields["id"], "nombre" => $res->fields["nombre"], "descripcion" => $res->fields["descripcion"], "idpadre" => $res->fields["idpadre"], "estado" => $res->fields["estado"], "url" => $res->fields["url"], "target" => $res->fields["target"]);

			$res->MoveNext();
		}

		//$prin["menu"] = $hijos_res;

		return $array_response;
	}

	public function consult_menu($id_menu, $array_response)
	{
		$sql = "SELECT * FROM menu WHERE idpadre = $id_menu AND estado  = 1";
		$res_padre = $this->BD->consultar($sql);
		$idp = 0;

		while (!$res_padre->EOF) {

			$array_response[] = array("cad" => "", "id" => $res_padre->fields["id"], "nombre" => $res_padre->fields["nombre"], "descripcion" => $res_padre->fields["descripcion"], "idpadre" => $res_padre->fields["idpadre"], "estado" => $res_padre->fields["estado"], "url" => $res_padre->fields["url"], "target" => $res_padre->fields["target"]);

			$idp = $res_padre->fields["id"];

			$this->consult_menu($idp, $array_response);

			$res_padre->MoveNext();
		}

		return $array_response;
	}

	public function show($id_perfil, $nom_pro)
	{
		$prin = array();
		$resp = array();
		$check = "";
		$this->array_response = array();

		$squ = "SELECT descripcion FROM perfiles WHERE id = $id_perfil";
		$resu = $this->BD->consultar($squ);
		// 	$nombre_perfil = strtoupper($resu->fields["descripcion"]); PHP 5.3
		$nombre_perfil = "";
		foreach ($resu as $row) {
			$nombre_perfil = $row["descripcion"]; //PHP 8
		}

		$sql = "SELECT * FROM menu WHERE idpadre = 0 AND estado  = 1 ORDER BY orden";
		$res = $this->BD->consultar($sql);

		while (!$res->EOF) {

			$sql = "SELECT id_menu FROM menu_perfiles WHERE id_menu = " . $res->fields["id"] . " and id_perfil = " . $id_perfil . " and estado = 1";
			$res_per = $this->BD->consultar($sql);

			$checked = 0;

			if ($this->BD->numreg($res_per) > 0) {
				$checked = 1;
			}

			$this->array_response[] = array("checked" => $checked, "cad" => "", "id" => $res->fields["id"], "nom_perfil" => $nombre_perfil, "nombre" => $res->fields["nombre"], "descripcion" => $res->fields["descripcion"], "idpadre" => $res->fields["idpadre"], "estado" => $res->fields["estado"], "url" => $res->fields["url"], "target" => $res->fields["target"]);
			$prin["menu"] = $this->consult_hijos($res->fields["id"], 0, $id_perfil);
			$res->MoveNext();
		}
		if ($nom_pro != "") {
			$prin["perfiles"] = $this->Get_Profile($nom_pro);
		} else {
			$prin["perfiles"] = $this->Retornar_Perfiles();
		}

		return $prin;
	}

	public function consult_hijos($id_menu, $id_hijo, $id_perfil)
	{
		$check = "";

		$sql = "SELECT * FROM menu WHERE idpadre = $id_menu AND estado  = 1";
		$res = $this->BD->consultar($sql);

		while (!$res->EOF) {

			$cad = "";

			$sql = "SELECT id_menu FROM menu_perfiles WHERE id_menu = " . $res->fields["id"] . " and id_perfil = " . $id_perfil . " and estado = 1";
			$res_per = $this->BD->consultar($sql);

			$checked = 0;

			if ($this->BD->numreg($res_per) > 0) {
				$checked = 1;
			}

			if ($id_hijo == 0) {
				$this->con_niv[$res->fields["id"]] = 1;
			} else {
				$niv = $this->con_niv[$id_hijo];
				$this->con_niv[$res->fields["id"]] = $niv + 1;
			}

			for ($x = 0; $x < $this->con_niv[$res->fields["id"]]; $x++) {
				$cad .= "─ ";
			}

			$this->array_response[] = array("checked" => $checked, "cad" => "|" . $cad, "id" => $res->fields["id"], "nombre" => $res->fields["nombre"], "descripcion" => $res->fields["descripcion"], "idpadre" => $res->fields["idpadre"], "estado" => $res->fields["estado"], "url" => $res->fields["url"], "target" => $res->fields["target"]);
			$this->consult_hijos($res->fields["id"], $res->fields["id"], $id_perfil);
			$res->MoveNext();
		}

		return $this->array_response;
	}

	public function Val_Check($id_perfil, $id_menu)
	{
		$sql = "SELECT id_menu FROM menu_perfiles WHERE id_menu = " . $id_menu . " and id_perfil = " . $id_perfil . " and estado = 1";
		$res = $this->BD->consultar($sql);

		$checked = 0;

		if ($this->BD->numreg($res) > 0) {
			$checked = 1;
		}

		return $checked;
	}
} // Fin clase

<?php
include_once("../../config/mainController.php"); // Incluye el Controlador Principal
include_once("./modelo.php");	// Incluye el Modelo.
$controller = new mainController(); // Instancia a la clase MainController
$modelo = new admin_menu_perfiles(); // Instancia a la clase del modelo
try // Try, manejo de Errores
{
	$metodo = $_SERVER['REQUEST_METHOD'];
	$tipo_res = "";
	$response = null;
	// Se manejaran dos tipos JSON y HTML
	// Dependiendo del método de la petición ejecutaremos la acción correspondiente.
	// Por ahora solo POST, todas las llamadas se haran por POST
	$variables = $_POST;
	if (!isset($_POST['accion'])) {
		echo "0";
		return;
	} // Evita que ocurra un error si no manda accion.
	$accion = $variables['accion'];
	// Dependiendo de la accion se ejecutaran las tareas y se definira el tipo de respuesta.
	switch ($accion) {
		//Lista de nombres de los perfiles
		case 'ver_nom_perfil':
			$tipo_res = 'JSON';
			$response = $modelo->Retornar_nom_perfil();
			break;
		case 'Crear_Perfil':
			$tipo_res = 'HTML';
			$nom_perfil = $variables["nom_perfil"];
			$response = $modelo->Crear_Nuevo_Perfil($nom_perfil);
			break;
		//Cargar menú en el div para la creacion
		case 'load_menu':
			$tipo_res = 'JSON';
			$response = $modelo->Retornar_Menu();
			break;
		case 'val_check':
			$tipo_res = 'HTML';
			$id_perfil = $variables["id_perfil"];
			$id_menu = $variables["id_menu"];
			$response = $modelo->Val_Check($id_perfil, $id_menu);
			break;
		case 'show':
			$tipo_res = 'JSON';
			$id_perfil = $variables["id_perfil"];
			$nom_pro = $variables["nom_pro"];
			$response = $modelo->show($id_perfil, $nom_pro);
			break;
		case 'Crea_Perfil_Accesos':
			$tipo_res = 'HTML';
			$id_perfiles = $variables["id_perfiles"];
			$checkboxValues = $variables["checkboxValues"];
			$response = $modelo->Crear_Perfil_Accesos($id_perfiles, $checkboxValues);
			break;
		//Listar todos los campos de los perfiles
		case 'Consultar_Perfiles':
			$nombre_perfil = $variables["nombre_perfil"];
			$estado_perfil = $variables["estado_perfil"];
			$tipo_res = 'JSON';
			$response = $modelo->Retornar_Perfiles();
			break;
		//Cambiar el estado de los perfiles
		case 'Cambiar_Estado':
			$tipo_res = "HTML";
			$id_perfil = $variables["id_perfil"];
			$estado_perfil = $variables["estado_perfil"];
			$response = $modelo->Cambiar_Estado($id_perfil, $estado_perfil);
			break;
		//Validar y cambiar el estado del campo 'sms'
		case 'Val_SMS':
			$tipo_res = "HTML";
			$id_perfil = $variables["id_perfil"];
			$val_sms = $variables["val_sms"];
			$response = $modelo->Validar_SMS($id_perfil, $val_sms);
			break;
		//Validar y cambiar el estado del campo 'email'
		case 'Val_Email':
			$tipo_res = "HTML";
			$id_perfil = $variables["id_perfil"];
			$val_email = $variables["val_email"];
			$response = $modelo->Validar_Email($id_perfil, $val_email);
			break;
		//Insertar o Editar perfiles, segun el 'id' del perfil
		//Id vacio, Crea un nuevo registro
		//Id con valor, Edita el perfil con ese id
		case 'Editar_Perfil':
			$tipo_res = "HTML";
			$id_perfil = $variables["id_perfil"];
			$nombre_perfil = $variables["nombre_perfil"];
			$response = $modelo->Editar_Perfil($id_perfil, $nombre_perfil);
			break;
	}

	// Respuestas del Controlador
	if ($tipo_res == "JSON") {
		echo json_encode($response, true); // $response será un array con los datos de nuestra respuesta.
	} elseif ($tipo_res == "HTML") {
		echo $response; // $response será un html con el string de nuestra respuesta.
	}
} // Fin Try
catch (Exception $e) {
}

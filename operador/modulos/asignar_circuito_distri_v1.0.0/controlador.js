const URL_MODULO = 'modulos/asignar_circuito_distri_v1.0.0';

$(document).ready(function () {
	funcion_cargar_circuitos("b_ruta", "")
	//funcion_cargar_distribuidores("b_distri",1)
	//funcion_cargar_regionales("b_regional",1)
	cargar_distri()
	cargar_regional()
	Cargar_Tabla_Rutas_Distri();
	$("#frm_asig").submit(function (event) {
		event.preventDefault();
		Asignar_Distribuidor_Ruta();
	})

	$("#Consultar_Rutas").click(function () {
		Cargar_Tabla_Rutas_Distri();
	})
	$('#stockist').change(function () {
		cargar_regional_distri();
	})
});
var route;
function Cargar_Tabla_Rutas_Distri() {
	ruta = $("#b_ruta").val();
	distri = $("#b_distri").val();
	reg = $("#b_regional").val();
	if (ruta == undefined) { ruta = ""; }
	if (!$.fn.DataTable.isDataTable('#t_rutas')) {
		dtable = $("#t_rutas").DataTable({
			"ajax": function (data, callback, settings) {
				fetch(`${URL_MODULO}/controlador.php`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						accion: 'Consultar_Rutas_Distri',
						ruta: ruta,
						distri: distri,
						reg: reg
					})
				}).then(response => response.json()).then(result => {
					callback({
						data: result
					});
				}).catch(error => {
					console.error('Error:', error);
				});
			},
			"bFilter": false, "responsive": true,
			"columns": [
				{ "data": "cod_route" },
				{ "data": "name_route" },
				{ "data": "stockist" },
				{ "data": "regional" },
				{ "data": "user" },
				{ "data": "fecha" },
				{ "data": "route" }
			],
			"columnDefs": [
				{
					"targets": 6,
					"data": "",
					render: function (data, type, row) {
						return '<button class="btn btn-sm btn-primary asign"><i class="glyphicon glyphicon-pushpin"></i></button>';
					}
				}],
			fnDrawCallback: function () {
				$(".asign").unbind("click");
				funcion_cargar_distribuidores("stockist", 1);
				$(".asign").click(function () {
					var data = dtable.row($(this).parents('tr')).data();
					$(".modal").modal();
					$("#stockist").val(data['stockist_id']).change();
					route = data['route'];
					cargar_regional_distri(data['regional_id'])
				})
			}
		});
		$("#resultados").show();
	}
	else {
		dtable.destroy();
		Cargar_Tabla_Rutas_Distri();
	}

}
function cargar_regional_distri(reg_id) {
	var datos = formToJSON("#frm_asig");
	fetch(`${URL_MODULO}/controlador.php`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			accion: 'cargar_regional_distri',
			datos: datos
		})
	}).then(response => response.json()).then(data => {
		var option = "<option value=''>Seleccione regional</option>"
		$.each(data, function (index, fila) {
			option += "<option value='" + fila.reg + "'>" + fila.nombre + "</option>";
		});
		$('#stockist_reg').html(option)
		$("#stockist_reg").val(reg_id).change();
	}).catch(error => {
		console.error('Error:', error);
	});
}

function Asignar_Distribuidor_Ruta() {
	var datos = formToJSON("#frm_asig");
	datos.route = route;
	fetch(`${URL_MODULO}/controlador.php`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			accion: 'Asignar_Distribuidor_Ruta',
			datos: datos
		})
	}).then(response => response.json()).then(data => {
		if (data['res'] == 1) {
			Notificacion(data['msg'], "success");
			$(".modal").modal("hide");
			Cargar_Tabla_Rutas_Distri()
		}
		else if (data['res'] == -1) {
			Notificacion(data['msg'], "error");
		}
		else {
			Notificacion("Error al intentar guardar los datos de la bodega", "error");
		}
	}).catch(error => {
		console.error('Error:', error);
	});
}
function cargar_distri() {
	fetch(`${URL_MODULO}/controlador.php`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			accion: 'cargar_distri'
		})
	}).then(response => response.json()).then(data => {
		var option = "<option value=''>SELECCIONAR</option>";
		var option_act = "<optgroup label='DISTRIBUIDORES ACTIVOS'>";
		var option_inac = "<optgroup label='DISTRIBUIDORES INACTIVOS'>";
		$.each(data, function (index, fila) {
			if (fila.estado == 1) {
				option_act += "<option value='" + fila.id + "'>" + fila.nombre + "</option>";
			} else {
				option_inac += "<option value='" + fila.id + "'>" + fila.nombre + "</option>";
			}
		});
		option_act += "</optgroup>";
		option_inac += "</optgroup>";
		if (option_act != '') {
			option += option_act;
		}
		if (option_inac != '') {
			option += option_inac;
		}
		option += "<option value='NULL'>Sin Distribuidor</option>";
		$('#b_distri').html(option)
		$("#b_distri").val('').change();
	}).catch(error => {
		console.error('Error:', error);
	});
}
function cargar_regional() {
	fetch(`${URL_MODULO}/controlador.php`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			accion: 'cargar_regional'
		})
	}).then(response => response.json()).then(data => {
		var option = "<option value=''>SELECCIONAR</option><option value='NULL'>Sin Regional</option>"
		$.each(data, function (index, fila) {
			option += "<option value='" + fila.id + "'>" + fila.nombre + "</option>";
		});
		$('#b_regional').html(option)
		$("#b_regional").val('').change();
	}).catch(error => {
		console.error('Error:', error);
	});
}

<?php
require("../../config/mainModel.php");
class asignar_ruta_distri
{
  /*Metodos de la DB.
    $this->consultar_datos($query); // Realiza la consulta y devuelve un Array Asociativo con los datos
    $this->ejecuta_query($query); // Ejecuta Insert, Update, Delete, Alter, Drop.. / devuelve el ultimo id afectado.
    $this->total_registros($columna, $tabla, $where = ""); // Devuelve el numero de registros. count();
    $this->trae_uno($query); // Realiza una consulta, devolviendo el primer valor que encuentre.
    $this->begin_work();
    $this->commit();
    $this->rollback();
  */
  //Espacio Para declara las funciones que retornan los datos de la DB.
  private $DB;
  public function __construct()
  {
    $BD = new BD();
    $this->DB = $BD;
    $this->DB->conectar();
  }
  public function __destruct()
  {
    $this->DB->desconectar();
  }

  public function Consultar_Rutas_Distri($ruta, $distri, $reg)
  {
    $sql = "";
    if ($ruta != "") {
      $sql .= " AND r.id = '$ruta'";
    }
    if ($distri != "" && $distri != 'NULL') {
      $sql .= " AND td.id_distri  = $distri";
    }
    if ($reg != "" && $reg != 'NULL') {
      $sql .= " AND td.id_regional = $reg";
    }
    $consulta = "SELECT  r.id route, r.nombre cod_route,r.descripcion name_route, d.id stockist_id, concat(d.nombre,' ',if(d.estado=1,'(ACTIVO)','(INACTIVO)')) stockist, re.id regional_id, re.nombre regional
                FROM territorios r
                LEFT JOIN territorios_distribuidor td ON r.id = td.id_territorio
                LEFT JOIN regional re ON re.id = td.id_regional
                LEFT JOIN distribuidores d ON d.id = td.id_distri 
                WHERE 1 $sql";
    $res = $this->DB->consultar($consulta);
    if ($distri != 'NULL' && $reg != 'NULL') {
      while (!$res->EOF) {
        $sql1 = "SELECT autd.fecha fecha, CONCAT(u.nombre,' ',u.apellido) user
                  FROM audi_territorios_distribuidor autd
                  INNER JOIN usuarios u ON u.id = autd.usuario
                  WHERE autd.id_territorio = " . $res->fields["route"] . "
                  ORDER BY autd.id DESC
                  Limit 1";
        $res1 = $this->DB->consultar($sql1);
        $datos[] = array(
          "route" => $res->fields["route"],
          "cod_route" => $res->fields["cod_route"],
          "name_route" => $res->fields["name_route"],
          "stockist_id" => $res->fields["stockist_id"],
          "stockist" => $res->fields["stockist"],
          "regional_id" => $res->fields["regional_id"],
          "regional" => $res->fields["regional"],
          "fecha" => ($res1->EOF) ? "" : $res1->fields["fecha"], // PHP 8.0
          "user" => ($res1->EOF) ? "" : $res1->fields["user"]
        ); // PHP 8.0
        //"fecha" => $res1->fields["fecha"], PHP 5.4
        //"user" => $res1->fields["user"]); PHP 5.4
        $res->MoveNext();
      }
    } else {
      while (!$res->EOF) {
        if ($res->fields["stockist_id"] == NULL) {
          $sql1 = "SELECT autd.fecha fecha, CONCAT(u.nombre,' ',u.apellido) user
                  FROM audi_territorios_distribuidor autd
                  INNER JOIN usuarios u ON u.id = autd.usuario
                  WHERE autd.id_territorio = " . $res->fields["route"] . "
                  ORDER BY autd.id DESC
                  Limit 1";
          $res1 = $this->DB->consultar($sql1);
          $datos[] = array("route" => $res->fields["route"], "cod_route" => $res->fields["cod_route"], "name_route" => $res->fields["name_route"], "stockist_id" => $res->fields["stockist_id"], "stockist" => $res->fields["stockist"], "regional_id" => $res->fields["regional_id"], "regional" => $res->fields["regional"], "fecha" => $res1->fields["fecha"], "user" => $res1->fields["user"]);
        }
        $res->MoveNext();
      }
    }
    if (!isset($datos))
      $datos = array();

    return $datos;
  }
  public function cargar_regional_distri($datos)
  {
    $consulta = "SELECT rd.id_reg reg, r.nombre nombre FROM regionales_distri rd
                INNER JOIN regional r ON rd.id_reg = r.id
                WHERE rd.id_distri = " . $datos['stockist'] . "
                AND r.estado = 1";
    $reg = $this->DB->devolver_array($consulta);
    return $reg;
  }
  public function cargar_distri()
  {
    $consulta = "SELECT id,nombre,estado FROM distribuidores";
    $reg = $this->DB->devolver_array($consulta);
    return $reg;
  }
  public function cargar_regional()
  {
    $consulta = "SELECT id,nombre FROM regional WHERE estado = 1";
    $reg = $this->DB->devolver_array($consulta);
    return $reg;
  }

  public function Asignar_Distribuidor_Ruta($datos)
  {


    $oSession = unserialize($_SESSION[$GLOBALS["SESION_OP"]]);
    $id = $oSession->VSid;
    $idusu = $id;
    /*CARGA DE REGIONAL ANTERIOR*/
    $c_reg_ant = "SELECT id_regional FROM {$GLOBALS["BD_NAME"]}.territorios_distribuidor WHERE id_territorio = {$datos['route']}";
    $r_reg_ant = $this->DB->devolver_array($c_reg_ant);
    /*CARGA DE REGIONAL ANTERIOR*/

    /*
      $BD->consultar("BEGIN");
    $BD->consultar("ROLLBACK");
    $BD->consultar("COMMIT");
      */

    $c_ruta = "SELECT z.id,td.id_distri,z.territorio,td.id_regional
                    FROM {$GLOBALS["BD_NAME"]}.territorios_distribuidor td
                    INNER JOIN {$GLOBALS["BD_NAME"]}.zonas z ON (td.id_territorio = z.territorio) 
                    WHERE td.id_regional={$datos["stockist_reg"]} ";
    $r_ruta = $this->DB->devolver_array($c_ruta);

    if (count($r_ruta) == 1) {
      $error = 0;
      $c_bd_distri = "SELECT concat('{$GLOBALS["BD_DIS"]}',nombre_corto,'_',id)as bd_nom FROM {$GLOBALS["BD_NAME"]}.distribuidores WHERE id = {$r_ruta[0]["id_distri"]} ";
      $r_bd_distri = $this->DB->devolver_array($c_bd_distri);

      $c_usu = "SELECT if(group_concat(id)is null,0,group_concat(id))as id FROM {$r_bd_distri[0]["bd_nom"]}.usuarios WHERE repartidor = 1 OR vendedor = 1";
      $r_usu = $this->DB->devolver_array($c_usu);

      $c_per_dcs = "SELECT id_usus FROM {$r_bd_distri[0]["bd_nom"]}.niveles_dcs WHERE tipo = 1 AND id_tipo = {$datos["stockist_reg"]};";
      $r_per_dcs = $this->DB->devolver_array($c_per_dcs);
      $this->DB->consultar("BEGIN");

      if (count($r_per_dcs) > 0 && count($r_usu) > 0) {

        $i_audi_dcs = "INSERT INTO {$r_bd_distri[0]["bd_nom"]}.audi_niveles_dcs(tipo,id_tipo,id_usus,p1,p2,p3,p4,fecha,hora,usuario,movimiento)
                          (SELECT 4,{$r_ruta[0]["id"]},id_usus,0,{$r_ruta[0]["territorio"]},1,{$r_ruta[0]["id_regional"]},curdate(),curtime(),{$idusu},3
                           FROM {$r_bd_distri[0]["bd_nom"]}.niveles_dcs WHERE tipo = 1 AND id_usus in ({$r_usu[0]["id"]}) AND id_tipo = {$datos["stockist_reg"]})";
        if (!$this->DB->consultar($i_audi_dcs)) {
          $error = 1;
        }

        $i_per_dcs = "INSERT INTO {$r_bd_distri[0]["bd_nom"]}.niveles_dcs(tipo,id_tipo,id_usus,p1,p2,p3,p4,f_insert,f_update)
                        (SELECT 4,{$r_ruta[0]["id"]},id_usus,0,{$r_ruta[0]["territorio"]},1,{$r_ruta[0]["id_regional"]},curdate(),null 
                         FROM {$r_bd_distri[0]["bd_nom"]}.niveles_dcs 
                         WHERE tipo = 1 AND id_usus in ({$r_usu[0]["id"]}) AND id_tipo = {$datos["stockist_reg"]})";

        if (!$this->DB->consultar($i_per_dcs)) {
          $error = 1;
        }

        $i_audi_dcsr = "INSERT INTO {$r_bd_distri[0]["bd_nom"]}.audi_niveles_dcs(tipo,id_tipo,id_usus,p1,p2,p3,p4,fecha,hora,usuario,movimiento)
                          (SELECT tipo,id_tipo,id_usus,p1,p2,p3,p4,curdate(),curtime(),{$idusu},4
                           FROM {$r_bd_distri[0]["bd_nom"]}.niveles_dcs 
                           WHERE tipo = 1 AND id_usus in ({$r_usu[0]["id"]}) AND id_tipo = {$datos["stockist_reg"]})";
        if (!$this->DB->consultar($i_audi_dcsr)) {
          $error = 1;
        }

        $d_per_dcs = "DELETE FROM {$r_bd_distri[0]["bd_nom"]}.niveles_dcs WHERE tipo = 1 AND id_usus in ({$r_usu[0]["id"]}) AND id_tipo = {$datos["stockist_reg"]};";
        if (!$this->DB->consultar($d_per_dcs)) {
          $error = 1;
        }

        if ($error != 1) {
          $this->DB->consultar("COMMIT");
        } else {
          $this->DB->consultar("ROLLBACK");
          return array('res' => -1, 'msg' => 'No se puede realizar la asignación');
        }
      }
    }


    if ($datos['stockist'] == "") {
      $datos['stockist'] = 0;
      $tipo = 0;
    }

    $sql = "SELECT now() fecha_sin";
    $res_fecha = $this->DB->consultar($sql);

    $fechaSin = $res_fecha->fields["fecha_sin"];

    //Update!
    $consult = "SELECT id FROM territorios_distribuidor WHERE id_territorio = " . $datos['route'];
    $res = $this->DB->consultar($consult);
    if ($this->DB->numreg($res) > 0) {


      $this->DB->consultar("BEGIN");
      $update = "UPDATE territorios_distribuidor SET id_distri = " . $datos['stockist'] . ", id_regional = " . $datos['stockist_reg'] . " WHERE id_territorio = " . $datos['route'];
      if ($this->DB->consultar($update)) {
        $update = "INSERT INTO `audi_territorios_distribuidor`(`id`, `id_territorio_distri`, `id_territorio`, `id_distri`, `id_regional`, `estado`, `fecha`, `hora`, `usuario`) VALUES (null," . $res->fields['id'] . "," . $datos['route'] . "," . $datos['stockist'] . "," . $datos['stockist_reg'] . ",1,curdate(),curtime(),$id)";
        $this->DB->consultar($update);
        $this->DB->consultar("COMMIT");

        //actualiza el distribuidor en la tabla de territorios
        $update_territorio = "UPDATE territorios SET id_rgnal = " . $datos['stockist_reg'] . " WHERE id = " . $datos['route'];
        $this->DB->consultar($update_territorio);
        $this->DB->consultar("COMMIT");

        $res = array('res' => 1, 'msg' => 'Se realizo con exito la asignación');
      } else {
        $this->DB->consultar("ROLLBACK");
        $res = array('res' => -1, 'msg' => 'No se puede realizar la asignación');
      }
    } else {


      $this->DB->consultar("BEGIN");
      $insert = "INSERT INTO territorios_distribuidor (`id`, `id_territorio`, `id_distri`, `estado`, `id_regional`) VALUES (null," . $datos['route'] . "," . $datos['stockist'] . ",1," . $datos['stockist_reg'] . ")";
      if ($this->DB->consultar($insert)) {
        $ultimo = $this->DB->last_insert_id();
        $update = "INSERT INTO `audi_territorios_distribuidor`(`id`, `id_territorio_distri`, `id_territorio`, `id_distri`, `id_regional`, `estado`, `fecha`, `hora`, `usuario`) VALUES (null," . $ultimo . "," . $datos['route'] . "," . $datos['stockist'] . "," . $datos['stockist_reg'] . ",1,curdate(),curtime(),$id)";
        $this->DB->consultar($update);
        $this->DB->consultar("COMMIT");

        //actualiza el distribuidor en la tabla de territorios
        $update_territorio = "UPDATE territorios SET id_rgnal = " . $datos['stockist_reg'] . " WHERE id = " . $datos['route'];


        $this->DB->consultar($update_territorio);
        $this->DB->consultar("COMMIT");

        $res = array('res' => 1, 'msg' => 'Se realizo con exito la asignación');

        $this->DB->envio_noti_sync(1, $fechaSin);
      } else {
        $this->DB->consultar("ROLLBACK");
        $res = array('res' => -1, 'msg' => 'No se puede realizar la asignación');
      }
    }

    // BORRAR PERMISOS DCS --- 

    // OPERADOR..!

    $delete_d = "DELETE FROM niveles_dcs WHERE tipo = 4 AND id_tipo = '" . $datos['route'] . "'";
    $delete_t = "DELETE FROM niveles_dcs WHERE tipo != 4 AND p2 = '" . $datos['route'] . "'";
    $this->DB->consultar($delete_d);
    $this->DB->consultar($delete_t);

    // DISTRIBUIDOR
    $consulta_db_distri = "SELECT concat('{$GLOBALS["BD_DIS"]}',nombre_corto,'_',id) db FROM distribuidores";
    $datos_db_distri = $this->DB->devolver_array($consulta_db_distri);
    for ($d = 0; $d < count($datos_db_distri); $d++) {
      $database = $datos_db_distri[$d]['db'];

      $this->DB->consultar("use $database"); // Base de datos del distribuidor.!
      $delete_d = "DELETE FROM niveles_dcs WHERE tipo = 3 AND id_tipo = '" . $datos['route'] . "'"; // Zonas con permisos total.!
      $delete_t = "DELETE FROM niveles_dcs WHERE tipo != 3 AND p2 = '" . $datos['route'] . "'"; // Puntos de esa zona con permisos.!
      $this->DB->consultar($delete_d);
      $this->DB->consultar($delete_t);
    }

    /*CUANDO SE BORRA UNA RUTA*/
    if (count($r_reg_ant) > 0) {
      $c_ter = "SELECT z.id,td.id_distri,z.territorio as id_territorio,td.id_regional
                      FROM {$GLOBALS["BD_NAME"]}.territorios_distribuidor td
                      INNER JOIN {$GLOBALS["BD_NAME"]}.zonas z ON (td.id_territorio = z.territorio) 
                      WHERE td.id_regional={$r_reg_ant[0]["id_regional"]} 
                      GROUP BY td.id_territorio";
      $r_ter = $this->DB->devolver_array($c_ter);

      if (count($r_ter) == 1) {
        $error = 0;
        $c_bd_distri = "SELECT concat('{$GLOBALS["BD_DIS"]}',nombre_corto,'_',id)as bd_nom FROM {$GLOBALS["BD_NAME"]}.distribuidores WHERE id = {$r_ter[0]["id_distri"]} ";
        $r_bd_distri = $this->DB->devolver_array($c_bd_distri);
        $c_usu = "SELECT if(group_concat(id)is null,0,group_concat(id))as id FROM {$r_bd_distri[0]["bd_nom"]}.usuarios WHERE repartidor = 1 OR vendedor = 1";
        $r_usu = $this->DB->devolver_array($c_usu);
        $c_per_dcs = "SELECT id_usus FROM {$r_bd_distri[0]["bd_nom"]}.niveles_dcs WHERE tipo = 4 AND id_tipo = {$r_ter[0]["id"]};";
        $r_per_dcs = $this->DB->devolver_array($c_per_dcs);
        $this->DB->consultar("BEGIN");
        if (count($r_per_dcs) > 0 && count($r_usu) > 0) {


          $i_per_dcs = "INSERT INTO {$r_bd_distri[0]["bd_nom"]}.niveles_dcs(tipo,id_tipo,id_usus,p1,p2,p3,p4,f_insert,f_update)
                          (SELECT 1,{$r_reg_ant[0]["id_regional"]},id_usus,0,0,1,0,curdate(),null 
                           FROM {$r_bd_distri[0]["bd_nom"]}.niveles_dcs 
                           WHERE tipo = 4 AND id_usus in ({$r_usu[0]["id"]}) AND id_tipo = {$r_ter[0]["id"]})";

          if (!$this->DB->consultar($i_per_dcs)) {
            $error = 1;
          }

          $i_audi_dcsr = "INSERT INTO {$r_bd_distri[0]["bd_nom"]}.audi_niveles_dcs(tipo,id_tipo,id_usus,p1,p2,p3,p4,fecha,hora,usuario,movimiento)
                            (SELECT tipo,id_tipo,id_usus,0,0,1,{$r_reg_ant[0]["id_regional"]},curdate(),curtime(),{$idusu},4
                             FROM {$r_bd_distri[0]["bd_nom"]}.niveles_dcs WHERE tipo = 1 AND id_usus in ({$r_usu[0]["id"]}) AND id_tipo = {$datos["stockist_reg"]})";
          if (!$this->DB->consultar($i_audi_dcsr)) {
            $error = 1;
          }

          $i_audi_dcs = "INSERT INTO {$r_bd_distri[0]["bd_nom"]}.audi_niveles_dcs(tipo,id_tipo,id_usus,p1,p2,p3,p4,fecha,hora,usuario,movimiento)
                            (SELECT tipo,id_tipo,id_usus,0,0,1,{$r_reg_ant[0]["id_regional"]},curdate(),curtime(),{$idusu},4
                             FROM {$r_bd_distri[0]["bd_nom"]}.niveles_dcs WHERE tipo = 4 AND id_usus in ({$r_usu[0]["id"]}) AND id_tipo = {$r_ter[0]["id"]})";
          if (!$this->DB->consultar($i_audi_dcs)) {
            $error = 1;
          }

          $d_per_dcs = "DELETE FROM {$r_bd_distri[0]["bd_nom"]}.niveles_dcs WHERE tipo = 4 AND id_usus in ({$r_usu[0]["id"]}) AND id_tipo = {$r_ter[0]["id"]};";
          if (!$this->DB->consultar($d_per_dcs)) {
            $error = 1;
          }
        }

        if ($error != 1) {
          $this->DB->consultar("COMMIT");
        } else {
          $this->DB->consultar("ROLLBACK");
        }
      }
    }
    /*CUANDO SE BORRA UNA RUTA*/

    return $res;
  }
} // Fin clase

const URL_MODULO = 'modulos/asignar_distri_regional';
var tipo_ac = 0,
    iddistri = 0,
    reg_bod, reg_bod_name, loop1, loop2, loop3, loop4, reg_name,
    html, regionales, dtable, option, change, inputs, bodegas;

$(document).ready(function () {
    cargar_regional();
    funcion_cargar_distribuidores("nom_distri");

    $("#frm2").submit(function (event) {
        event.preventDefault();

        $("#buscar_crear").show();

        Cargar_Tabla_distribuidores();
    });

    $("#frm_reg").submit(function (event) {
        event.preventDefault();

        if (Object.keys(formToJSON("#frm_reg")).length > 0) {
            $("#listBod").html("");

            run_guardar();
        } else {
            Notificacion("DCS no permite que el distribuidor no tenga regional asignada, por favor seleccione una regional.", "warning");
        }
    });

    $("#guardar_bod").submit(function (event) {
        event.preventDefault();
        reg_bod = $("#reg_bod").val();
        reg_bod_name = $("#reg_bod option:selected").attr("name");

        if (reg_bod) {
            BootstrapDialog.confirm("¿Seguro desea guardar las bodegas en la regional <b>" + reg_bod_name + "</b>?", function (result) {
                (result) ? guardar_regional(bodegas, reg_bod) : undefined;
            });
        } else {
            Notificacion("Tiene que seleccionar en que regional se guardaran las bodegas", "warning");
        }
    });

    $("#chectodos").click(function () {
        if ($("#chectodos").is(":checked")) {
            $("input.chk").prop("checked", true);
        } else {
            $("input.chk").prop("checked", false);
        }
    });
});

function run_guardar() {
    $.post('modulos/asignar_distri_regional/controlador.php', {
        accion: 'obtener_bodegas',
        distri: iddistri
    }, function (data, textStatus) {
        data = JSON.parse(data);
        change = false;
        regionales = formToJSON("#frm_reg").chk.split(",");
        inputs = $("#frm_reg input[data-initial]");
        bodegas = [];

        for (loop3 = 0; loop3 < inputs.length; loop3++) {
            if (!$(inputs[loop3]).is(":checked")) {
                change = true;

                for (loop4 = 0; loop4 < data.length; loop4++) {
                    if (data[loop4]["id_regdistri"] == $(inputs[loop3]).val()) {
                        bodegas[bodegas.length] = data[loop4];
                    }
                }
            }
        }

        console.log(bodegas);

        if (bodegas.length > 0 && change == true) {
            /* 
             * SI TIENE BODEGAS
             */
            if (regionales.length > 1) {
                $("#subModal").modal();

                html = "<ul style='margin: 10px 0;'>";

                for (loop1 = 0; loop1 < bodegas.length; loop1++) {
                    html += "<li>" + bodegas[loop1]["nombre_bodega"] + "</li>";
                }

                html += "</ul>";
                html += "<div class='col-md-12'>";
                html += "<select id='reg_bod' class='form-control'>";
                html += "   <option value=''>SELECCIONAR</option>"

                for (loop2 = 0; loop2 < regionales.length; loop2++) {
                    reg_name = $("#" + "reg" + regionales[loop2]).attr("name");

                    html += "   <option name='" + reg_name + "' value='" + regionales[loop2] + "'>" + reg_name + "</option>";
                }

                html += "</select>";
                html += "</div>";

                $("#listBod").append(html);
            } else {
                reg_bod = formToJSON("#frm_reg").chk;
                reg_bod_name = $("#" + "reg" + formToJSON("#frm_reg").chk).attr("name");

                BootstrapDialog.confirm("¿Seguro desea guardar los cambios?<br />Las bodegas se guardaran en la regional <b>" + reg_bod_name + "</b>", function (result) {
                    (result) ? guardar_regional(bodegas, reg_bod) : undefined;
                });
            }
        } else {
            /* 
             * NO TIENE BODEGAS
             */
            guardar_regional("", "");
        }
    });
}

function guardar_regional(bodegas, regional) {
    $.post('modulos/asignar_distri_regional/controlador.php', {
        accion: 'asignar_reginal',
        iddis: iddistri,
        datos: formToJSON("#frm_reg"),
        bodegas: bodegas,
        reg_bod: regional
    }, function (data, textStatus) {
        data = JSON.parse(data);

        if (data["id"] == 1) {
            Cargar_Tabla_distribuidores();

            Notificacion(data["msg"], "success");

            $("#principalModal, #subModal").modal("hide");
        } else if (data["id"] == 0) {
            Notificacion(data["msg"], "warning");
        } else {
            Notificacion("No se pudieron guardar los datos del distribuidor", "error");
        }
    });
}

function Cargar_Tabla_distribuidores() {
    var nom_distri = $("#nom_distri").val(),
        reg = $("#regb").val(),
        cod_distri = $("#cod_distri").val(),
        nit = $("#nit").val(),
        cdis = "",
        nitdis = "";

    if (!$.fn.DataTable.isDataTable('#tbldistri')) {
        dtable = $("#tbldistri").DataTable({
            "ajax": {
                "url": "modulos/asignar_distri_regional/controlador.php",
                "type": "POST",
                "deferRender": false,
                "data": {
                    accion: 'Consultar_Distribuidores',
                    nom_distri: nom_distri,
                    cod_distri: cod_distri,
                    nit: nit,
                    nitdis: nitdis,
                    reg: reg
                }
            }, "bFilter": false,
            "responsive": true,
            "iDisplayLength": 25,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "Todo"]],
            "columns": [
                { "data": "cod_distri" },
                { "data": "nit" },
                { "data": "nombre" },
                { "data": "direccion" },
                { "data": "depto" },
                { "data": "muni" },
                { "data": "nombre" }
            ], "columnDefs": [{
                "targets": 6,
                "data": "",
                render: function (data, type, row) {
                    return '<button class="btn btn-sm btn-default edit"><i class="glyphicon glyphicon-zoom-in"></i></button></td>';
                }
            }], fnDrawCallback: function () {
                $(".edit").unbind("click");
                $(".edit").click(function () {
                    var data = dtable.row($(this).parents('tr')).data();
                    iddistri = data['id'];
                    ver_regionales(iddistri, data['nombre']);
                });
            }
        });
    } else {
        dtable.destroy();

        Cargar_Tabla_distribuidores();
    }
}

function cargar_regional() {
    $.post('modulos/asignar_distri_regional/controlador.php', {
        accion: 'cargar_regional'
    }, function (data, textStatus) {
        data = JSON.parse(data);
        option = "<option value='' selected>SELECCIONAR</option>";

        $.each(data, function (index, fila) {
            option += "<option value='" + fila.id + "'>" + fila.nombre + "</option>";
        });

        $('#regb').html(option).change();
    });
}

function ver_regionales(iddistri, distri) {
    $("#principalModal").modal();
    $("#nomdis").html(distri);
    $("#chectodos").prop("checked", false);

    $.post('modulos/asignar_distri_regional/controlador.php', {
        accion: 'ver_regionales',
        iddis: iddistri
    }, function (data, textStatus) {
        data = JSON.parse(data);

        $.each(data, function (index, fila) {
            $("#reg").clone().attr("id", "reg" + fila.idreg).insertBefore("#reg").attr("name", fila.nombre);

            if (fila.estado == 1) {
                $("#reg" + fila.idreg).html('<input name="chk" class="chk" type="checkbox" id="chk" value="' + fila.idreg + '" data-initial checked/>&nbsp;&nbsp;' + fila.nombre);
            } else {
                $("#reg" + fila.idreg).html('<input name="chk" class="chk" type="checkbox" id="chk" value="' + fila.idreg + '"/>&nbsp;&nbsp;' + fila.nombre);
            }
        });

        $("#reg").remove();
    });
}
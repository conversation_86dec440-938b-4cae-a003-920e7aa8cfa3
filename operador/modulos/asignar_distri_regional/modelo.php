<?php
if (!isset($_SESSION)) {
    session_start();
}

require_once("../../config/Session.php");
require("../../config/mainModel.php");

class asignar_regional_distri
{
    private $DB;
    private $user;
    private $fechaSin;
    public function __construct()
    {
        $BD = new BD();
        $this->DB = $BD;
        $this->DB->conectar();
        $oSession = unserialize($_SESSION[$GLOBALS["SESION_OP"]]);
        $this->user = $oSession->VSid;
        $res_fecha = $this->DB->consultar("SELECT now() fecha_sin");
        $this->fechaSin = $res_fecha->fields["fecha_sin"];
    }

    public function __destruct()
    {
        $this->DB->desconectar();
    }

    public function obtener_bodegas($distri)
    {
        return $this->DB->devolver_array("SELECT id, nombre_bodega, id_regdistri FROM {$GLOBALS["BD_NAME"]}.bodega WHERE id_dependencia = '{$distri}'");
    }

    public function asignar_reginal($distri, $reg, $bodegas, $reg_bod)
    {
        $this->DB->consultar("BEGIN");

        $error = false;
        $regionales = explode(",", $reg["chk"]);

        if ($bodegas != "" && $reg_bod != "") {
            foreach ($bodegas as $key => $value) {
                // ACTUALIZAR LA REGIONAL A LA QUE PERTENECE LA BODEGA
                $this->DB->consultar("UPDATE bodega SET id_regdistri = '{$reg_bod}' WHERE id = '{$value["id"]}'");
            }
        }

        foreach ($regionales as $valor) {
            $reg_query = $this->DB->consultar("SELECT id FROM {$GLOBALS["BD_NAME"]}.regionales_distri WHERE id_reg = '$valor' AND id_distri = '$distri'");

            if (intval($reg_query->fields["id"]) <= 0) {
                $reg_insert =  $this->DB->consultar("INSERT INTO {$GLOBALS["BD_NAME"]}.regionales_distri (id_reg, id_distri) VALUES ('$valor', '$distri')");

                if (!$reg_insert) {
                    $error = true;
                }
            }
        }

        $id_reg = $this->DB->consultar("SELECT id_reg FROM {$GLOBALS["BD_NAME"]}.regionales_distri WHERE id_distri = '$distri'");

        while (!$id_reg->EOF) {
            $action = false;

            foreach ($regionales as $valor) {
                if ($valor == $id_reg->fields["id_reg"]) {
                    $action = true;
                }
            }

            if (!$action) {
                $reg_del = $this->DB->consultar("DELETE FROM {$GLOBALS["BD_NAME"]}.regionales_distri WHERE id_reg = '{$id_reg->fields["id_reg"]}' AND id_distri = '$distri'");

                if (!$reg_del) {
                    $error = true;
                }

                /*
                 * ELIMINAR PERMISOS DCS
                 */
                $this->DB->consultar("DELETE FROM {$GLOBALS["BD_NAME"]}.niveles_dcs WHERE tipo = 3 AND id_tipo = '$distri' AND p5 = '{$id_reg->fields["id_reg"]}'");
                $this->DB->consultar("DELETE FROM {$GLOBALS["BD_NAME"]}.niveles_dcs WHERE tipo != 3 AND p3 = '$distri' AND p5 = '{$id_reg->fields["id_reg"]}'");
                $this->DB->consultar("DELETE FROM {$GLOBALS["BD_NAME"]}.territorios_distribuidor WHERE id_distri = '$distri' AND id_regional = '{$id_reg->fields["id_reg"]}'");

                // DISTRIBUIDOR
                $db_distri = $this->DB->devolver_array("SELECT CONCAT('{$GLOBALS["BD_DIS"]}', nombre_corto, '_', id) AS db FROM {$GLOBALS["BD_NAME"]}.distribuidores WHERE id = '$distri'");

                for ($loop = 0; $loop < count($db_distri); $loop++) {
                    $database = $db_distri[$loop]["db"];

                    /*
                     * ELIMINAR PERMISOS DCS DEL DISTRIBUIDOR
                     */
                    $this->DB->consultar("DELETE FROM $database.niveles_dcs WHERE tipo = 1 AND id_tipo = '{$id_reg->fields["id_reg"]}'");
                    $this->DB->consultar("DELETE FROM $database.niveles_dcs WHERE tipo != 1 AND p4 = '{$id_reg->fields["id_reg"]}'");
                }
            }

            $id_reg->MoveNext();
        }

        if (!$error) {
            $this->DB->consultar("COMMIT");
            $this->DB->envio_noti_sync(1, $this->fechaSin);

            $response = array("id" => 1, "msg" => "Los datos se han actualizado con exito");
        } else {
            $this->DB->consultar("ROLLBACK");

            $response = array("id" => 0, "msg" => "Se produjo un error al intentar guardar los datos");
        }

        return $response;
    }

    public function cargar_regional()
    {
        $condicion = "";

        if ($this->user != 1) {
            $permisos = $this->DB->Permisos_Dcs_Regionales($this->user);
            $condicion .= "AND r.id in ($permisos)";
        }

        $sql = "SELECT r.id AS id, r.nombre AS nombre FROM regional AS r INNER JOIN canal_regional AS cr ON r.id = cr.id_regional WHERE cr.id_canal = 1 AND r.estado = 1 AND cr.estado = 1 $condicion";

        return $this->DB->devolver_array($sql);
    }

    public function ver_regionales($iddistri)
    {
        $sql = "SELECT r.id AS idreg, IF(rd.id IS NULL,'0','1') AS estado, '$iddistri' AS iddis, r.nombre 
                FROM {$GLOBALS["BD_NAME"]}.regional AS r 
                LEFT JOIN regionales_distri AS rd ON (r.id = rd.id_reg AND rd.id_distri = $iddistri) WHERE r.estado = 1";

        return $this->DB->devolver_array($sql);
    }

    public function Retornar_Distribuidor($nombre, $cod_distri, $nitdis, $reg)
    {
        $resp = array();
        $array_response = array();
        $cont = 0;
        $band = false;
        $having = "";

        $sql = "SELECT 
                    dis.id, dis.nombre, dis.cod_distri, dis.direccion, dis.nit, mpd.nombre AS depto, mpm.nombre AS muni, GROUP_CONCAT(regdis.id_reg) AS regionales 
                FROM {$GLOBALS["BD_NAME"]}.distribuidores AS dis 
                LEFT JOIN {$GLOBALS["BD_NAME"]}.regionales_distri AS regdis ON (regdis.id_distri = dis.id) 
                INNER JOIN {$GLOBALS["BD_NAME"]}.departamentos AS mpd ON (mpd.id = dis.depto) 
                INNER JOIN {$GLOBALS["BD_NAME"]}.municipios AS mpm ON (mpm.id = dis.ciudad AND mpm.departamento = dis.depto)";

        if ($nombre != "") {
            if ($band) {
                $sql .= " AND dis.id = '{$nombre}'";
            } else {
                $sql .= " WHERE dis.id = '{$nombre}'";
                $band = true;
            }
        }

        if ($cod_distri != "") {
            if ($band) {
                $sql .= " AND dis.cod_distri like '%" . $cod_distri . "%'";
            } else {
                $sql .= " WHERE dis.cod_distri like '%" . $cod_distri . "%'";
                $band = true;
            }
        }

        if ($nitdis != "") {
            if ($band) {
                $sql .= " AND dis.nit like '%$nitdis%'";
            } else {
                $sql .= " WHERE dis.nit like '%$nitdis%'";
                $band = true;
            }
        }

        if ($reg != "") {
            $having = "HAVING regionales like '%$reg%'";
            $band = false;
        }

        $sql .= " GROUP BY dis.id $having ORDER BY dis.id DESC";

        return $this->DB->devolver_array($sql);
    }
}

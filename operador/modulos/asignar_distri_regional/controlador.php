<?php
include_once("../../config/mainController.php");
include_once("../../modulos/asignar_distri_regional/modelo.php");

$controller = new mainController;
$asreg = new asignar_regional_distri();

try {
    $tipo_res = "";
    $response = null;
    $variables = $_POST;

    // evita que ocurra un error si no manda accion
    if (!isset($_POST['accion'])) {
        echo "0";
        return;
    }

    $accion = $variables['accion'];

    switch ($accion) {
        case 'obtener_bodegas':
            $tipo_res = 'JSON';
            $distri = $variables["distri"];
            $response = $asreg->obtener_bodegas($distri);
            break;

        case 'asignar_reginal':
            $tipo_res = 'JSON';
            $distri = intval($variables['iddis']);
            $bodegas = $variables['bodegas'];
            $reg_bod = $variables['reg_bod'];

            if (isset($variables['datos'])) {
                $regionales = $variables['datos'];
            } else {
                $regionales = 0;
            }

            $response = $asreg->asignar_reginal($distri, $regionales, $bodegas, $reg_bod);
            break;

        case 'Consultar_Distribuidores':
            $tipo_res = 'JSON';
            $nombre = $variables['nom_distri'];
            $cod_distri = $variables['cod_distri'];
            $nitdis = $variables['nit'];
            $reg = $variables['reg'];
            $datos = $asreg->Retornar_Distribuidor($nombre, $cod_distri, $nitdis, $reg);

            $response = array(
                "draw" => 1,
                "recordsTotal" => 0,
                "recordsFiltered" => 0,
                "data" => $datos
            );
            break;

        case 'ver_regionales':
            $tipo_res = 'JSON';
            $distri = intval($variables['iddis']);
            $response = $asreg->ver_regionales($distri);
            break;

        case 'cargar_regional':
            $tipo_res = "JSON";
            $response = $asreg->cargar_regional();
            break;
    }

    if ($tipo_res == "JSON") {
        echo json_encode($response, true);
    } else if ($tipo_res == "HTML") {
        echo $response;
    }
} catch (Exception $e) {
}

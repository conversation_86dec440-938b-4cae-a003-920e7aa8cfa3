<?php
include_once("../../config/mainController.php");
include_once("../../modulos/asignar_distri_regional/modelo.php");

$controller = new mainController;
$asreg = new asignar_regional_distri();

try {
    $metodo = $_SERVER['REQUEST_METHOD'];
    $tipo_res = "";
    $response = null;
    $variables = array();

    if (!empty($_POST)) {
        $variables = $_POST;
    } else if ($metodo == 'POST' && $_SERVER['CONTENT_TYPE'] == 'application/json') {
        // leer json
        $json = file_get_contents('php://input');
        $variables = json_decode($json, true);
    }

    // Evita que ocurra un error si no manda accion.
    if (!isset($variables['accion'])) {
        http_response_code(404);
        header("Content-type: text/plain; charset=utf-8");
        echo "0";
        return;
    }

    $accion = $variables['accion'];

    switch ($accion) {
        case 'obtener_bodegas':
            $tipo_res = 'JSON';
            $distri = $variables["distri"];
            $response = $asreg->obtener_bodegas($distri);
            break;

        case 'asignar_reginal':
            $tipo_res = 'JSON';
            $distri = intval($variables['iddis']);
            $bodegas = $variables['bodegas'];
            $reg_bod = $variables['reg_bod'];

            if (isset($variables['datos'])) {
                $regionales = $variables['datos'];
            } else {
                $regionales = 0;
            }

            $response = $asreg->asignar_reginal($distri, $regionales, $bodegas, $reg_bod);
            break;

        case 'Consultar_Distribuidores':
            $tipo_res = 'JSON';
            $nombre = $variables['nom_distri'];
            $cod_distri = $variables['cod_distri'];
            $nitdis = $variables['nit'];
            $reg = $variables['reg'];
            $datos = $asreg->Retornar_Distribuidor($nombre, $cod_distri, $nitdis, $reg);

            $response = array(
                "draw" => 1,
                "recordsTotal" => 0,
                "recordsFiltered" => 0,
                "data" => $datos
            );
            break;

        case 'ver_regionales':
            $tipo_res = 'JSON';
            $distri = intval($variables['iddis']);
            $response = $asreg->ver_regionales($distri);
            break;

        case 'cargar_regional':
            $tipo_res = "JSON";
            $response = $asreg->cargar_regional();
            break;
        default:
            http_response_code(404);
            header("Content-type: text/plain; charset=utf-8");
            echo "0";
            return;
    }

    // Respuestas del Controlador
    if ($tipo_res == "JSON") {
        header("Content-type: application/json; charset=utf-8");
        $json_output = json_encode($response, true); // $response será un array con los datos de nuestra respuesta.
        if (json_last_error() !== JSON_ERROR_NONE) {
            // Si hubo un error en la codificación JSON
            throw new Exception("Error al codificar la respuesta JSON: " . json_last_error_msg());
        } else {
            echo $json_output;
        }
    } elseif ($tipo_res == "HTML") {
        header("Content-type: text/html; charset=utf-8");
        echo $response; // $response será un html con el string de nuestra respuesta.
    } else {
        header("Content-type: text/plain; charset=utf-8");
        echo $response; // $response será un texto plano con el string de nuestra respuesta.
    }
} catch (Exception $e) {
    header("Content-type: application/json; charset=utf-8");
    http_response_code(500);
    echo json_encode(array("error" => true, "mensaje" => $e->getMessage()));
} catch (Error $e) {
    header("Content-type: application/json; charset=utf-8");
    http_response_code(500);
    echo json_encode(array("error" => true, "mensaje" => $e->getMessage()));
}
